<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Notification - Triumph Enterprises</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
        h1 {
            color: #4a5568;
        }
        .success-icon {
            text-align: center;
            font-size: 48px;
            color: #48bb78;
            margin: 20px 0;
        }
        .failure-icon {
            text-align: center;
            font-size: 48px;
            color: #e53e3e;
            margin: 20px 0;
        }
        .payment-details {
            background-color: #f0fff4;
            padding: 15px;
            border-left: 4px solid #48bb78;
            margin: 15px 0;
        }
        .payment-details.failed {
            background-color: #fff5f5;
            border-left-color: #e53e3e;
        }
        .customer-info {
            background-color: #e6f7ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Payment {{ payment.status|title }} Notification</h1>
    </div>
    
    <div class="content">
        {% if payment.status == "COMPLETED" %}
        <div class="success-icon">✓</div>
        {% else %}
        <div class="failure-icon">✗</div>
        {% endif %}
        
        <p>This is a notification about a payment for order #{{ order.id }}.</p>
        
        <div class="payment-details {% if payment.status != 'COMPLETED' %}failed{% endif %}">
            <p><strong>Order Number:</strong> {{ order.id }}</p>
            <p><strong>Payment Date:</strong> {{ payment.created_at|date:"F d, Y, g:i a" }}</p>
            <p><strong>Payment Method:</strong> {{ payment.payment_method }}</p>
            <p><strong>Transaction ID:</strong> {{ payment.transaction_id }}</p>
            <p><strong>Amount:</strong> ₹{{ payment.amount }}</p>
            <p><strong>Status:</strong> {{ payment.status }}</p>
        </div>
        
        <div class="customer-info">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> {{ order.user.name }} </p>
            <p><strong>Email:</strong> {{ order.user.email }}</p>
            <p><strong>Phone:</strong> {{ order.user.phone_number|default:"Not provided" }}</p>
        </div>
        
        {% if payment.status == "COMPLETED" %}
        <p>The payment has been successfully processed. The order is now ready to be fulfilled.</p>
        {% else %}
        <p>The payment has failed. You may want to contact the customer to arrange an alternative payment method.</p>
        {% endif %}
    </div>
    
    <div class="footer">
        <p>This is an automated notification from your website.</p>
        <p>&copy; 2024 Triumph Enterprises. All rights reserved.</p>
    </div>
</body>
</html>
