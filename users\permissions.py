from rest_framework.permissions import BasePermission, SAFE_METHODS

class IsAuthenticatedOrCreateOnly(BasePermission):
    """
    Custom permission to only allow unauthenticated users to create objects.
    Authenticated users can perform any action.
    """
    def has_permission(self, request, view):
        # Allow any user to create (POST) new objects
        if request.method == 'POST':
            return True
        # Otherwise, require the user to be authenticated for all other actions
        return request.user and request.user.is_authenticated
