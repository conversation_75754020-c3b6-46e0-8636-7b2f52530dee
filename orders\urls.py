# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from .views import CartViewSet, OrderViewSet, ShippingMethodViewSet

# router = DefaultRouter()
# router.register(r'cart', CartViewSet, basename='cart')
# router.register(r'orders', OrderViewSet, basename='order')
# router.register(r'shipping-methods', ShippingMethodViewSet, basename='shipping-method')

# urlpatterns = [
#     path('', include(router.urls)),
# ]


from django.urls import path
from . import views

# Remove app_name to allow global URL names
# app_name = 'ecommerce'

urlpatterns = [
    # Cart URLs
    path('cart/', views.CartDetail.as_view(), name='cart-detail'),
    path('cart/add-item/', views.CartAddItem.as_view(), name='cart-add-item'),
    path('cart/update-item/', views.CartUpdateItem.as_view(), name='cart-update-item'),
    path('cart/clear/', views.CartClear.as_view(), name='cart-clear'),

    # Shipping URLs
    path('shipping-methods/', views.ShippingMethodList.as_view(), name='shipping-method-list'),

    # Order URLs - Use the URL names expected by the tests
    path('', views.OrderList.as_view(), name='order-list'),  # This matches the test expectation
    path('admin/orders/', views.OrderFilterList.as_view(), name='order-filters-list'),
    path('<str:pk>/', views.OrderDetail.as_view(), name='order-detail'),  # This matches the test expectation
    path('<str:pk>/cancel/', views.OrderCancel.as_view(), name='order-cancel'),  # This matches the test expectation
    path('<str:pk>/refund/', views.OrderRefund.as_view(), name='order-refund'),
    path('<str:pk>/update-status/', views.OrderUpdateStatus.as_view(), name='order-update-status'),
    path('<str:pk>/update-tracking/', views.OrderUpdateTracking.as_view(), name='order-update-tracking'),

    # Invoice URLs
    path('<str:order_id>/invoice/generate/', views.InvoiceGenerateView.as_view(), name='invoice-generate'),
    path('<str:order_id>/invoice/download/', views.InvoiceDownloadView.as_view(), name='invoice-download'),
]