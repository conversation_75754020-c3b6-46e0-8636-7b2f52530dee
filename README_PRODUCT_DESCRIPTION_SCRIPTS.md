# Product Description Generation Scripts

This directory contains scripts for generating and updating enhanced product descriptions using external data sources like Google and ecommerce platforms.

## Scripts Overview

### 1. `generate_product_descriptions.py`
**Main script** that retrieves all product details from the database and generates enhanced descriptions.

**Features:**
- Retrieves products from PostgreSQL database using .env configuration
- Generates detailed descriptions from Google and ecommerce platforms
- Saves complete product information as JSON files
- Comprehensive error handling and logging
- Rate limiting to avoid overwhelming external services

### 2. `update_product_descriptions.py`
**Update script** that updates only the description field in the database.

**Features:**
- Updates only product descriptions (preserves all other data)
- Reads from generated JSON files
- Creates backup before updating
- Dry-run mode for validation
- Batch processing with transaction safety

### 3. `test_product_scripts.py`
**Test script** to validate setup and dependencies.

**Features:**
- Tests database connection
- Validates product models and data
- Checks required dependencies
- Creates sample test data

## Prerequisites

### Database Configuration
Ensure your `.env` file contains the correct database connection details:

```env
DB_HOST=localhost
DB_NAME=triumph
DB_PASSWORD=triumph
DB_PORT=5432
DB_USER=triumph_user
DEVELOPMENT=True
```

### Required Dependencies
All dependencies are already included in `requirements.txt`:
- Django
- requests
- psycopg2-binary
- python-dotenv

## Usage Instructions

### Step 1: Test Setup
First, run the test script to ensure everything is configured correctly:

```bash
cd e-com-2024-apis
python test_product_scripts.py
```

This will:
- Test database connection
- Validate product data
- Check dependencies
- Create sample test data

### Step 2: Generate Enhanced Descriptions
Run the main generation script:

```bash
python generate_product_descriptions.py
```

This will:
- Retrieve all active products from the database
- Generate enhanced descriptions using Google and ecommerce data
- Save results to `data new qubo jsons/enhanced_products_YYYYMMDD_HHMMSS.json`
- Create detailed logs in `product_description_generation.log`

**Note:** This process may take time depending on the number of products and external API response times.

### Step 3: Update Database (Optional)
To update only the descriptions in the database:

```bash
python update_product_descriptions.py
```

This will:
- Find the latest generated JSON file
- Perform a dry run to validate data
- Ask for confirmation
- Create a backup of current descriptions
- Update only the description fields
- Log all changes in `product_description_update.log`

## Output Structure

### JSON File Format
The generated JSON files contain:

```json
{
  "metadata": {
    "generated_at": "2025-01-XX...",
    "total_products": 150,
    "script_version": "1.0",
    "description_source": "Google and ecommerce platforms"
  },
  "products": [
    {
      "id": 1,
      "name": "Product Name",
      "slug": "product-slug",
      "description": "Original description",
      "enhanced_description": "Enhanced description with features...",
      "category": "Category Name",
      "subcategory": "Subcategory Name",
      "brand": "Brand Name",
      "price": 1000.00,
      "mrp": 1000.00,
      "base_price": 847.46,
      "gst_rate": 18.0,
      "gst_amount": 152.54,
      "stock": 10,
      "is_active": true,
      "created_at": "2025-01-XX...",
      "updated_at": "2025-01-XX...",
      "images": ["path/to/image1.jpg", "path/to/image2.jpg"]
    }
  ]
}
```

### Directory Structure
```
e-com-2024-apis/
├── generate_product_descriptions.py
├── update_product_descriptions.py
├── test_product_scripts.py
├── data new qubo jsons/
│   ├── enhanced_products_20250XXX_XXXXXX.json
│   ├── description_backup_20250XXX_XXXXXX.json
│   └── sample_enhanced_products_test.json
├── product_description_generation.log
└── product_description_update.log
```

## Configuration Options

### Description Generation Settings
In `generate_product_descriptions.py`, you can modify:

```python
# Rate limiting
self.delay_between_requests = 2  # seconds between requests

# Batch processing
batch_size = 50  # products per batch
```

### Search Sources
The script searches these platforms:
- Google Search (general product information)
- Amazon.in
- Flipkart.com
- Snapdeal.com
- Shopclues.com

## Error Handling

### Common Issues and Solutions

1. **Database Connection Error**
   - Check `.env` file configuration
   - Ensure PostgreSQL is running
   - Verify database credentials

2. **No Products Found**
   - Check if products exist in database
   - Verify `is_active=True` filter
   - Run test script to validate data

3. **External API Rate Limiting**
   - Increase `delay_between_requests`
   - Run script during off-peak hours
   - Check logs for specific error messages

4. **Permission Errors**
   - Ensure write permissions for output directory
   - Check file system space
   - Verify user permissions

### Logging
All scripts create detailed logs:
- `product_description_generation.log` - Generation process
- `product_description_update.log` - Update process
- Console output for real-time monitoring

## Safety Features

### Backup and Recovery
- Automatic backup creation before updates
- Dry-run mode for validation
- Transaction-based updates (rollback on error)
- Detailed change logging

### Data Integrity
- Preserves all existing product data
- Updates only description fields
- Validates data before processing
- Error recovery and continuation

## Performance Considerations

### Optimization Tips
1. Run during off-peak hours to avoid rate limiting
2. Monitor external API response times
3. Use batch processing for large datasets
4. Consider running in background for large product catalogs

### Expected Runtime
- Small catalog (< 100 products): 10-30 minutes
- Medium catalog (100-500 products): 30-90 minutes  
- Large catalog (> 500 products): 1-3 hours

## Support and Troubleshooting

### Debug Mode
Enable debug logging by modifying the logging level:

```python
logging.basicConfig(level=logging.DEBUG, ...)
```

### Manual Intervention
If the script fails partway through:
1. Check the logs for the last processed product
2. The script can be safely rerun (it handles duplicates)
3. Use the backup files to restore if needed

### Contact
For issues or questions, check the logs first, then contact the development team with:
- Error messages from logs
- System configuration details
- Steps to reproduce the issue

---

**Last Updated:** January 2025  
**Version:** 1.0  
**Author:** Triumph Enterprise
