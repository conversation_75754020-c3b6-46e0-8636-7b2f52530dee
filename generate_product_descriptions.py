#!/usr/bin/env python3
"""
Product Description Generator Script

This script retrieves all product details from the database and generates
enhanced product descriptions by scraping information from Google and 
ecommerce platforms. The enhanced product data is saved as JSON files.

Features:
- Retrieves products from PostgreSQL database
- Generates detailed descriptions from external sources
- Saves complete product information as JSON
- Comprehensive error handling and logging
- Database connection management using .env configuration

Author: Triumph Enterprise
Created: 2025
"""

import os
import sys
import django
import json
import requests
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
import re
from dataclasses import dataclass, asdict
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    print("Warning: BeautifulSoup not available. Install with: pip install beautifulsoup4")

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from django.db import connection
from products.models import Product, Category, Brand, ProductImage, SubCategorie, GST
from django.core.serializers.json import DjangoJSONEncoder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('product_description_generation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductData:
    """Data class for product information"""
    id: int
    name: str
    slug: Optional[str]
    description: Optional[str]
    enhanced_description: Optional[str]
    category: Optional[str]
    subcategory: Optional[str]
    brand: Optional[str]
    price: float
    mrp: float
    base_price: float
    gst_rate: float
    gst_amount: float
    stock: int
    is_active: bool
    created_at: str
    updated_at: str
    images: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)

class DescriptionGenerator:
    """Handles description generation from external sources"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.delay_between_requests = 2  # seconds
        
    def search_google_for_product(self, product_name: str, brand: str = None) -> Optional[str]:
        """Search Google for detailed product information"""
        try:
            # Construct comprehensive search query
            search_terms = []
            if brand:
                search_terms.append(f'"{brand}"')
            search_terms.append(f'"{product_name}"')
            search_terms.extend(["specifications", "features", "review", "price"])

            query = " ".join(search_terms)
            encoded_query = quote_plus(query)

            # Google search URL with additional parameters for better results
            url = f"https://www.google.com/search?q={encoded_query}&num=10"

            logger.info(f"Searching Google for: {query}")

            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            # Parse with BeautifulSoup if available
            if HAS_BEAUTIFULSOUP:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Extract product information from search results
                descriptions = []

                # Look for product descriptions in various elements
                for element in soup.find_all(['div', 'span', 'p'], limit=20):
                    text = element.get_text(strip=True)
                    if (len(text) > 50 and len(text) < 500 and
                        any(keyword in text.lower() for keyword in
                            ['feature', 'specification', 'product', 'quality', 'performance', 'design'])):
                        descriptions.append(text)

                if descriptions:
                    # Return the most relevant description
                    best_desc = max(descriptions, key=len)
                    return self.clean_description_text(best_desc)

            else:
                # Fallback regex extraction
                content = response.text
                description_patterns = [
                    r'<div[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)</div>',
                    r'<span[^>]*>([^<]{100,400})</span>',
                    r'<p[^>]*>([^<]{100,400})</p>',
                ]

                for pattern in description_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    if matches:
                        cleaned = self.clean_description_text(matches[0])
                        if len(cleaned) > 50:
                            return cleaned

            time.sleep(self.delay_between_requests)
            return None

        except Exception as e:
            logger.error(f"Error searching Google for {product_name}: {str(e)}")
            return None

    def clean_description_text(self, text: str) -> str:
        """Clean and format description text"""
        try:
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', text)
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text)
            # Remove special characters but keep basic punctuation
            text = re.sub(r'[^\w\s\-\.,;:()%]', '', text)
            # Capitalize first letter
            text = text.strip()
            if text:
                text = text[0].upper() + text[1:]
            return text
        except Exception:
            return text
    
    def search_ecommerce_sites(self, product_name: str, brand: str = None) -> Optional[str]:
        """Search ecommerce sites for detailed product information"""
        try:
            # Enhanced ecommerce sites with specific search strategies
            ecommerce_sites = [
                {"name": "amazon.in", "priority": 1},
                {"name": "flipkart.com", "priority": 2},
                {"name": "snapdeal.com", "priority": 3},
                {"name": "myntra.com", "priority": 4},
                {"name": "ajio.com", "priority": 5}
            ]

            # Clean product name for better search
            clean_product_name = re.sub(r'[^\w\s]', ' ', product_name).strip()

            for site_info in ecommerce_sites:
                site = site_info["name"]
                try:
                    # Construct targeted search query
                    search_terms = []
                    if brand:
                        search_terms.append(f'"{brand}"')
                    search_terms.append(f'"{clean_product_name}"')
                    search_terms.append("buy online")

                    site_query = f"site:{site} {' '.join(search_terms)}"
                    encoded_query = quote_plus(site_query)
                    url = f"https://www.google.com/search?q={encoded_query}&num=5"

                    logger.info(f"Searching {site} for: {clean_product_name}")

                    response = self.session.get(url, timeout=15)
                    response.raise_for_status()

                    # Parse with BeautifulSoup if available
                    if HAS_BEAUTIFULSOUP:
                        soup = BeautifulSoup(response.content, 'html.parser')

                        # Look for product titles and descriptions
                        product_info = []

                        # Extract from search result titles and snippets
                        for result in soup.find_all(['h3', 'div'], limit=10):
                            text = result.get_text(strip=True)
                            if (len(text) > 30 and len(text) < 300 and
                                any(word.lower() in text.lower() for word in clean_product_name.split()[:2])):
                                product_info.append(text)

                        if product_info:
                            # Return the most comprehensive description
                            best_info = max(product_info, key=len)
                            cleaned = self.clean_description_text(best_info)
                            if len(cleaned) > 50:
                                time.sleep(self.delay_between_requests)
                                return cleaned

                    else:
                        # Fallback regex extraction
                        content = response.text

                        # Enhanced patterns for ecommerce sites
                        ecommerce_patterns = [
                            r'<h3[^>]*>([^<]*' + re.escape(clean_product_name.split()[0]) + r'[^<]*)</h3>',
                            r'<div[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)</div>',
                            r'<span[^>]*class="[^"]*product[^"]*"[^>]*>([^<]+)</span>',
                        ]

                        for pattern in ecommerce_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            if matches:
                                for match in matches:
                                    cleaned = self.clean_description_text(match)
                                    if len(cleaned) > 50:
                                        time.sleep(self.delay_between_requests)
                                        return cleaned

                    time.sleep(self.delay_between_requests)

                except Exception as e:
                    logger.warning(f"Error searching {site}: {str(e)}")
                    continue

            return None

        except Exception as e:
            logger.error(f"Error searching ecommerce sites for {product_name}: {str(e)}")
            return None
    
    def generate_enhanced_description(self, product_name: str, brand: str = None,
                                    category: str = None, existing_description: str = None) -> str:
        """Generate comprehensive, detailed product description"""
        try:
            logger.info(f"Generating enhanced description for: {product_name}")

            # Initialize description components
            description_components = {
                'title': '',
                'overview': '',
                'features': '',
                'specifications': '',
                'benefits': '',
                'category_info': ''
            }

            # 1. Create product title section
            if brand:
                description_components['title'] = f"{brand} {product_name}"
            else:
                description_components['title'] = product_name

            # 2. Use existing description as overview if substantial
            if existing_description and len(existing_description.strip()) > 20:
                description_components['overview'] = existing_description.strip()

            # 3. Search for detailed product information
            google_info = self.search_google_for_product(product_name, brand)
            if google_info and len(google_info) > 50:
                description_components['features'] = google_info

            # 4. Search ecommerce sites for additional details
            ecommerce_info = self.search_ecommerce_sites(product_name, brand)
            if ecommerce_info and len(ecommerce_info) > 50:
                description_components['specifications'] = ecommerce_info

            # 5. Generate category-specific information
            if category:
                category_info = self.generate_category_specific_description(category, product_name, brand)
                if category_info:
                    description_components['category_info'] = category_info

            # 6. Generate product benefits and features
            benefits = self.generate_product_benefits(product_name, brand, category)
            if benefits:
                description_components['benefits'] = benefits

            # 7. Combine all components into comprehensive description
            enhanced_description = self.compile_final_description(description_components)

            # 8. Ensure quality and minimum length
            if len(enhanced_description) < 100:
                enhanced_description = self.generate_comprehensive_fallback(product_name, brand, category)

            logger.info(f"Generated enhanced description ({len(enhanced_description)} chars)")
            return enhanced_description

        except Exception as e:
            logger.error(f"Error generating enhanced description for {product_name}: {str(e)}")
            return self.generate_comprehensive_fallback(product_name, brand, category)

    def compile_final_description(self, components: Dict[str, str]) -> str:
        """Compile final comprehensive description from components"""
        try:
            description_parts = []

            # Add title/header
            if components['title']:
                description_parts.append(f"**{components['title']}**")

            # Add overview
            if components['overview']:
                description_parts.append(f"Product Overview:\n{components['overview']}")

            # Add features from Google search
            if components['features']:
                description_parts.append(f"Key Features:\n{components['features']}")

            # Add specifications from ecommerce
            if components['specifications']:
                description_parts.append(f"Product Details:\n{components['specifications']}")

            # Add category information
            if components['category_info']:
                description_parts.append(f"Category Information:\n{components['category_info']}")

            # Add benefits
            if components['benefits']:
                description_parts.append(f"Benefits:\n{components['benefits']}")

            # Join with proper formatting
            if description_parts:
                return "\n\n".join(description_parts)
            else:
                return ""

        except Exception as e:
            logger.error(f"Error compiling description: {str(e)}")
            return ""

    def generate_product_benefits(self, product_name: str, brand: str = None, category: str = None) -> str:
        """Generate product benefits based on category and type"""
        try:
            benefits = []

            # Category-specific benefits
            category_benefits = {
                "Kitchen Appliance": [
                    "Enhances cooking efficiency and convenience",
                    "Designed for modern kitchen requirements",
                    "Energy-efficient operation for cost savings",
                    "Easy to clean and maintain"
                ],
                "Hood": [
                    "Effective smoke and odor removal",
                    "Quiet operation for peaceful cooking",
                    "Stylish design complements kitchen decor",
                    "Multiple speed settings for optimal performance"
                ],
                "Electronics": [
                    "Advanced technology for superior performance",
                    "User-friendly interface and controls",
                    "Reliable and durable construction",
                    "Energy-efficient design"
                ],
                "Locks": [
                    "Enhanced security and protection",
                    "Durable construction for long-lasting use",
                    "Easy installation and operation",
                    "Reliable locking mechanism"
                ],
                "Hardware": [
                    "High-quality materials for durability",
                    "Precision engineering for optimal performance",
                    "Suitable for professional and personal use",
                    "Corrosion-resistant finish"
                ]
            }

            # Get category-specific benefits
            if category and category in category_benefits:
                benefits.extend(category_benefits[category][:3])
            else:
                # Generic benefits
                benefits.extend([
                    "High-quality construction for reliability",
                    "Designed for optimal performance",
                    "Suitable for various applications"
                ])

            # Add brand-specific benefits
            if brand:
                if brand.lower() == "haier":
                    benefits.append("Backed by Haier's reputation for quality and innovation")
                elif brand.lower() == "godrej":
                    benefits.append("Trusted Godrej quality and reliability")
                else:
                    benefits.append(f"Backed by {brand}'s commitment to quality")

            return "• " + "\n• ".join(benefits)

        except Exception as e:
            logger.error(f"Error generating benefits: {str(e)}")
            return ""
    
    def generate_category_specific_description(self, category: str, product_name: str, brand: str = None) -> Optional[str]:
        """Generate detailed category-specific product information"""
        try:
            category_descriptions = {
                "Kitchen Appliance": f"This premium kitchen appliance is designed to enhance your culinary experience with advanced features and reliable performance. Perfect for modern kitchens, it combines functionality with style.",
                "Hood": f"This kitchen hood provides excellent ventilation and smoke extraction capabilities. Features advanced filtration technology and quiet operation for a pleasant cooking environment.",
                "Electronics": f"This electronic device incorporates cutting-edge technology for superior performance and user experience. Built with quality components for long-lasting reliability.",
                "Locks": f"This security lock provides enhanced protection with reliable locking mechanisms. Designed for easy installation and operation while maintaining high security standards.",
                "Hardware": f"This hardware component is engineered for durability and precision. Made with high-quality materials to ensure optimal performance in various applications.",
                "Furniture": f"This furniture piece combines style and functionality for modern living spaces. Crafted with attention to detail and quality materials.",
                "Tools": f"This tool is designed for professional and personal use with precision engineering. Built to withstand regular use while maintaining accuracy and performance."
            }

            # Get base description for category
            base_desc = category_descriptions.get(category,
                f"This {category.lower()} product is designed for optimal performance and reliability.")

            # Add brand-specific information
            if brand:
                if brand.lower() == "haier":
                    base_desc += " Haier's innovative technology ensures energy efficiency and user-friendly operation."
                elif brand.lower() == "godrej":
                    base_desc += " Godrej's trusted quality and engineering excellence provide long-lasting value."
                else:
                    base_desc += f" {brand}'s commitment to quality ensures reliable performance and customer satisfaction."

            return base_desc

        except Exception as e:
            logger.error(f"Error generating category description for {category}: {str(e)}")
            return None

    def generate_comprehensive_fallback(self, product_name: str, brand: str = None, category: str = None) -> str:
        """Generate comprehensive fallback description when external sources fail"""
        try:
            description_parts = []

            # Title section
            if brand:
                description_parts.append(f"**{brand} {product_name}**")
            else:
                description_parts.append(f"**{product_name}**")

            # Product overview
            overview = f"The {product_name}"
            if brand:
                overview += f" from {brand}"
            if category:
                overview += f" is a premium {category.lower()} designed for exceptional performance and reliability."
            else:
                overview += " is a high-quality product designed for exceptional performance and reliability."
            description_parts.append(f"Product Overview:\n{overview}")

            # Key features based on category
            features = []
            if category:
                category_features = {
                    "Kitchen Appliance": ["Advanced cooking technology", "Energy-efficient operation", "Easy-to-use controls", "Durable construction"],
                    "Hood": ["Powerful suction capacity", "Quiet operation", "LED lighting", "Easy maintenance"],
                    "Electronics": ["Latest technology", "User-friendly interface", "Energy efficient", "Reliable performance"],
                    "Locks": ["High security", "Durable materials", "Easy installation", "Smooth operation"],
                    "Hardware": ["Precision engineering", "Corrosion resistant", "Long-lasting", "Professional grade"]
                }
                features = category_features.get(category, ["High quality", "Reliable performance", "Durable construction", "User-friendly design"])
            else:
                features = ["Premium quality construction", "Reliable performance", "User-friendly design", "Long-lasting durability"]

            features_text = "• " + "\n• ".join(features)
            description_parts.append(f"Key Features:\n{features_text}")

            # Benefits
            benefits = [
                "Designed for optimal performance and efficiency",
                "Built with high-quality materials for longevity",
                "Suitable for both professional and personal use",
                "Backed by manufacturer warranty and support"
            ]

            if brand:
                if brand.lower() == "haier":
                    benefits.append("Haier's innovative technology and global expertise")
                elif brand.lower() == "godrej":
                    benefits.append("Godrej's trusted quality and Indian engineering excellence")
                else:
                    benefits.append(f"{brand}'s commitment to quality and customer satisfaction")

            benefits_text = "• " + "\n• ".join(benefits)
            description_parts.append(f"Benefits:\n{benefits_text}")

            return "\n\n".join(description_parts)

        except Exception as e:
            logger.error(f"Error generating comprehensive fallback description: {str(e)}")
            return f"**{product_name}** - High-quality product with excellent features and performance. Designed for reliability and user satisfaction."

class ProductDataExtractor:
    """Handles product data extraction from database"""

    def __init__(self):
        self.description_generator = DescriptionGenerator()

    def get_products_needing_descriptions(self) -> List[ProductData]:
        """Retrieve products that need better descriptions (no description or very short)"""
        try:
            logger.info("Retrieving products that need enhanced descriptions...")

            # Get all active products and filter by description length
            all_products = Product.objects.select_related(
                'category', 'subcategory', 'brand', 'gst'
            ).prefetch_related('images').filter(is_active=True)

            # Filter products that need descriptions (compatible with Django 5.0)
            products_needing_desc = []
            for product in all_products:
                desc_len = len(product.description or '')
                if desc_len < 100:  # Less than 100 characters or empty
                    products_needing_desc.append(product)

            # Sort by description length (shortest first)
            products_needing_desc.sort(key=lambda p: len(p.description or ''))

            product_data_list = []
            total_products = len(products_needing_desc)

            logger.info(f"Found {total_products} products needing enhanced descriptions out of {all_products.count()} total products")

            for index, product in enumerate(products, 1):
                try:
                    logger.info(f"Processing product {index}/{total_products}: {product.name}")

                    # Get product images
                    images = []
                    for img in product.images.all():
                        if img.image:
                            images.append(img.image.name)

                    # Generate enhanced description
                    enhanced_description = self.description_generator.generate_enhanced_description(
                        product_name=product.name,
                        brand=product.brand.name if product.brand else None,
                        category=product.category.name if product.category else None,
                        existing_description=product.description
                    )

                    # Create product data object
                    product_data = ProductData(
                        id=product.id,
                        name=product.name,
                        slug=product.slug,
                        description=product.description or "",
                        enhanced_description=enhanced_description,
                        category=product.category.name if product.category else None,
                        subcategory=product.subcategory.name if product.subcategory else None,
                        brand=product.brand.name if product.brand else None,
                        price=float(product.price),
                        mrp=float(product.mrp),
                        base_price=float(product.base_price),
                        gst_rate=float(product.get_gst_rate().rate),
                        gst_amount=float(product.calculate_gst_from_mrp()),
                        stock=product.stock,
                        is_active=product.is_active,
                        created_at=product.created_at.isoformat(),
                        updated_at=product.updated_at.isoformat(),
                        images=images
                    )

                    product_data_list.append(product_data)

                    # Add delay to avoid overwhelming external services
                    if index % 10 == 0:
                        logger.info(f"Processed {index} products, taking a short break...")
                        time.sleep(5)

                except Exception as e:
                    logger.error(f"Error processing product {product.name}: {str(e)}")
                    continue

            logger.info(f"Successfully processed {len(product_data_list)} products")
            return product_data_list

        except Exception as e:
            logger.error(f"Error retrieving products from database: {str(e)}")
            raise

    def save_products_to_json(self, products: List[ProductData], output_dir: str) -> str:
        """Save products to JSON file"""
        try:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_products_{timestamp}.json"
            filepath = os.path.join(output_dir, filename)

            # Convert products to dictionaries
            products_dict = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_products": len(products),
                    "script_version": "1.0",
                    "description_source": "Google and ecommerce platforms"
                },
                "products": [product.to_dict() for product in products]
            }

            # Save to JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(products_dict, f, indent=2, ensure_ascii=False, cls=DjangoJSONEncoder)

            logger.info(f"Successfully saved {len(products)} products to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving products to JSON: {str(e)}")
            raise

def main():
    """Main function to run the product description generation"""
    try:
        logger.info("Starting product description generation script...")

        # Initialize extractor
        extractor = ProductDataExtractor()

        # Get products that need enhanced descriptions
        products = extractor.get_products_needing_descriptions()

        if not products:
            logger.warning("No products found in database")
            return

        # Save to JSON file
        output_dir = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        filepath = extractor.save_products_to_json(products, output_dir)

        logger.info(f"Product description generation completed successfully!")
        logger.info(f"Enhanced product data saved to: {filepath}")
        logger.info(f"Total products processed: {len(products)}")

        # Print summary
        print("\n" + "="*60)
        print("PRODUCT DESCRIPTION GENERATION SUMMARY")
        print("="*60)
        print(f"Total products processed: {len(products)}")
        print(f"Output file: {filepath}")
        print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)

    except Exception as e:
        logger.error(f"Script execution failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
