# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from rest_framework_simplejwt.views import (
#     TokenObtainPairView,
#     TokenRefreshView,
#     TokenVerifyView,
# )
# from .views import UserViewSet, AddressViewSet, PaymentMethodViewSet, WishlistViewSet

# router = DefaultRouter()
# router.register(r'', UserViewSet)
# router.register(r'addresses', AddressViewSet, basename='address')
# router.register(r'payment-methods', PaymentMethodViewSet, basename='payment-method')
# router.register(r'wishlist', WishlistViewSet, basename='wishlist')

# urlpatterns = [
#     path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
#     path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
#     path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
#     path('', include(router.urls)),
# ]


from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
    TokenBlacklistView,
)
from .views import (
    UserListCreateView,
    UserProfileView,
    ChangePasswordView,
    AddressListCreateView,
    AddressDetailView,
    PaymentMethodListCreateView,
    WishlistListCreateView,
    WishlistToggleView,
    SocialLoginView,
    LoginView,
    UserDetailView,
    AdminLoginView,
    remove_wishlist,
    CustomerListView,
    StatisticsView,
    GraphDataView,
    CustomerProfileUpdateView,
    ContactFormView
)

urlpatterns = [
    # JWT Token URLs
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    path("token/blacklist/", TokenBlacklistView.as_view(), name="token_blacklist"),
    # User URLs
    path("", UserListCreateView.as_view(), name="user-list-create"),
    path("detail/", UserDetailView.as_view(), name="user-list-create"),
    path("social/login/", SocialLoginView.as_view(), name="social-login"),
    path("login/", LoginView.as_view(), name="login"),
    path("admin/login/", AdminLoginView.as_view(), name="login"),
    path("profile/", UserProfileView.as_view(), name="user-profile"),
    path("profile/update/", CustomerProfileUpdateView.as_view(), name="profile-update"),
    path("change-password/", ChangePasswordView.as_view(), name="user-change-password"),
    # Address URLs
    path("addresses/", AddressListCreateView.as_view(), name="address-list-create"),
    path("addresses/<int:pk>/", AddressDetailView.as_view(), name="address-detail"),
    # Payment Method URLs
    path(
        "payment-methods/",
        PaymentMethodListCreateView.as_view(),
        name="payment-method-list-create",
    ),
    # Wishlist URLs
    path("wishlist/", WishlistListCreateView.as_view(), name="wishlist-list-create"),
    path(
        "wishlist/toggle/<int:pk>/",
        WishlistToggleView.as_view(),
        name="wishlist-toggle",
    ),
    path("remove/wishlist/", remove_wishlist, name="remove-wishlist"),
    path("customers/", CustomerListView.as_view(), name="wishlist-list-create"),
    path('statistics/', StatisticsView.as_view(), name='statistics'),
    path('graph-data/', GraphDataView.as_view(), name='graph-data'),
    path('contact/', ContactFormView.as_view(), name='contact-form'),
]
