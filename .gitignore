# Python
**/__pycache__/
**/*.py[cod]
**/*.so
**/*.egg
**/*.egg-info/
**/dist/
**/build/
**/*.pyo
**/*.pyc

# Virtualenv
**/venv/
**/ENV/
**/env/
**/*.venv
pip-log.txt
pip-delete-this-directory.txt

# Node
**/node_modules/
**/npm-debug.log
**/yarn-debug.log
**/yarn-error.log
**/.pnp/
**/.pnp.js

# Yarn
**/.yarn/*
!**/.yarn/cache
!**/.yarn/patches
!**/.yarn/releases
!**/.yarn/plugins
!**/.yarn/sdks
!**/.yarn/versions

# Bun
**/.bun/

# Rust
**/target/
Cargo.lock

# OS generated files
**/.DS_Store
**/Thumbs.db

# Logs and databases
**/*.log
**/*.sql
**/*.sqlite

# IDEs and editors
**/.vscode/
**/.idea/
**/*.sublime-workspace
**/*.sublime-project

# Environment variables
**/.env
**/.env.*
**images
**load_data.py
**media/
**media/**
**/media/

**db.sqlite3

**__pycache__
**static

# Django migrations
**/migrations/*.py
# Migrations
**/migrations/
# Except for __init__.py files in migrations directories
!**/migrations/__init__.py

**docker-compose.yml
**newupdatecategories.py

products/management/commands/clean_products.py
products/management/commands/__init__.py
products/management/__init__.py
update_16052025_products.csv
test_order_emails.py
test_email.py
json_to_csv_converter.py
triumph-static-assets/
test_email.py
test_order_emails.py
json_to_csv_converter.py
products/management/__init__.py
products/management/commands/__init__.py
products/management/__init__.py
update_16052025_products.csv
products/management/commands/clean_products.py
import_qubo_products.py
export_today_products.py
cleanup_duplicate_images.py
media_path_update_report_20250521_142243.csv
media_path_update_report_20250521_142858.csv
update_media_paths.py

product_dirs.txt
import_haier_products.py
fix_backslash_paths.py

test_api_gst_response.py
test_dynamic_gst.py
test_email_gst_fix.py
test_email_gst_rate_fix.py
test_gst_calculation_fix.py
test_gst_functionality.py
test_gst_inclusive.py
test_gst_rules.py
test_gst_with_sqlite.py
test_invoice_download.py
test_invoice_fix_complete.py
test_invoice_gst_fix.py
test_invoice_gst_rules.py
 .coverage
GST_INCLUSIVE_IMPLEMENTATION.md
GST_RULES_IMPLEMENTATION_SUMMARY.md
INVOICE_DOWNLOAD_FIX_SUMMARY.md
package-lock.json
products/management/commands/setup_default_gst.py
simple_gst_test.py
