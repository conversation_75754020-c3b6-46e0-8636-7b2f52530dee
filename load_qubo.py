import os
import json
import requests

from concurrent.futures import ThreadPoolExecutor, as_completed
from decimal import Decimal
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from products.models import Category, Brand, Product, ProductImage


def load_qubo():
    with open("./data/response_tehus.json", "r") as f:
        data = json.load(f)
    return data


def download_image(image_url, image_path):
    if not image_url.startswith("http"):
        print(f"Invalid URL: {image_url}")
        return None
    response = requests.get(image_url)
    if response.status_code == 200:
        os.makedirs(os.path.dirname(image_path), exist_ok=True)
        with open(image_path, "wb") as f:
            f.write(response.content)
    return image_path


def process_product(product_data, count):
    # Handle Category

    category, _ = Category.objects.get_or_create(name=product_data["category"])
    brand, _ = Brand.objects.get_or_create(name=product_data["brand"])

    # Create or update Product
    product, created = Product.objects.update_or_create(
        name=product_data["name"],
        defaults={
            "description": product_data["description"]
            if product_data["description"]
            else "",
            "category": category,
            "brand": brand,
            "price": Decimal(product_data["sell_price"]),
            "stock": 10,
            "is_active": True,
        },
    )
    # Download and associate images if provided
    # if created:
    with ThreadPoolExecutor() as executor:
        futures = []
        for image_url in product_data["images"]:
            image_name = os.path.basename(image_url).split("?")[0]
            image_path = os.path.join("media/products", image_name)
            # with ThreadPoolExecutor(max_workers=5) as exe:
            executor.submit(download_image, image_url, image_path)

            futures = executor.map(download_image, image_url, image_path)
        for future in futures:
            image_path = future
            if image_path:
                if created:
                    ProductImage.objects.create(product=product, image=image_path)

    print(f"{count} - Product {'created' if created else 'updated'}: {product.name}")


def load_data():
    count = 0
    for product_data in load_qubo()["products"][0:]:
        process_product(product_data, count)
        count += 1


if __name__ == "__main__":
    load_data()
