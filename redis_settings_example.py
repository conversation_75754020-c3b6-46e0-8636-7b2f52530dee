"""
Redis Configuration Example for Django Settings

This file shows how to configure Redis in your Django settings.py file.
Copy the relevant sections to your backend/settings.py file.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-here')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', '0') == '1'

ALLOWED_HOSTS = os.environ.get('DJANGO_ALLOWED_HOSTS', '*').split(',')

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # Add your apps here
]

# Redis Cache Configuration
REDIS_URL = os.environ.get('REDIS_URL', 'redis://redis:6379/0')
REDIS_HOST = os.environ.get('REDIS_HOST', 'redis')
REDIS_PORT = os.environ.get('REDIS_PORT', '6379')
REDIS_DB = os.environ.get('REDIS_DB', '0')
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')

# Configure Django to use Redis for caching
CACHES = {
    "default": {
        "BACKEND": os.environ.get('CACHES_BACKEND', "django_redis.cache.RedisCache"),
        "LOCATION": os.environ.get('CACHES_LOCATION', f"redis://{REDIS_HOST}:{REDIS_PORT}/1"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": REDIS_PASSWORD if REDIS_PASSWORD else None,
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        },
        "KEY_PREFIX": "triumph_ecom"
    }
}

# Use Redis for session storage
SESSION_ENGINE = os.environ.get('SESSION_ENGINE', "django.contrib.sessions.backends.cache")
SESSION_CACHE_ALIAS = os.environ.get('SESSION_CACHE_ALIAS', "default")
SESSION_COOKIE_AGE = 86400  # 1 day in seconds
SESSION_COOKIE_SECURE = os.environ.get('DJANGO_SESSION_COOKIE_SECURE', '1') == '1'

# Cache timeout in seconds
CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT', 3600))  # 1 hour default

# Example of how to configure Celery with Redis (if you decide to use Celery)
# Uncomment if you install and configure Celery
"""
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Kolkata'
"""

# Example of how to use the cache in your views:
"""
from django.core.cache import cache
from django.views.decorators.cache import cache_page

# Cache a view for 1 hour
@cache_page(60 * 60)
def my_cached_view(request):
    # This view will be cached for 1 hour
    return render(request, 'template.html')

# Manually cache data
def another_view(request):
    # Try to get data from cache
    data = cache.get('my_data_key')
    
    if data is None:
        # Cache miss, generate the data
        data = expensive_operation()
        # Store in cache for 1 hour (3600 seconds)
        cache.set('my_data_key', data, 3600)
    
    return render(request, 'template.html', {'data': data})

# Invalidate cache when data changes
def update_data(request):
    # Update the data
    update_operation()
    
    # Invalidate the cache
    cache.delete('my_data_key')
    
    return redirect('success_page')
"""
