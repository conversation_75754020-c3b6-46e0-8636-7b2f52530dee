# E-Commerce API Test Suite

This directory contains comprehensive test cases for the e-commerce API, focusing on payment processing, configuration, stability, and robustness.

## Test Structure

The test suite is organized into the following categories:

1. **Configuration Tests** (`test_configuration.py`)
   - Environment configuration validation
   - Email settings validation
   - PhonePe API configuration validation

2. **Payment Processing Tests** (`test_payment_processing.py`)
   - PhonePe payment initiation
   - Payment callback handling
   - Webhook processing
   - Payment status verification

3. **Order Processing Tests** (`test_order_processing.py`)
   - Order creation
   - Order status updates
   - Order validation (minimum order value)

4. **Email Notification Tests** (`test_email_notifications.py`)
   - Order confirmation emails
   - Payment success emails
   - Payment failure emails

5. **Stability and Robustness Tests** (`test_stability_robustness.py`)
   - Error handling
   - Edge cases
   - Security validation

6. **PhonePe Service Tests** (`test_phonepe_service.py`)
   - Service initialization
   - Payment initiation
   - Payment status checking

## Running Tests

### Prerequisites

1. Make sure you have pytest installed:
   ```
   pip install pytest pytest-django pytest-cov pytest-mock
   ```

2. Ensure your environment variables are set up correctly (see `.env.example`).

### Database Considerations

The test suite is configured to use SQLite for testing instead of PostgreSQL to avoid database permission issues. If you want to use PostgreSQL for testing, you have two options:

1. **Use the `--keepdb` flag**: This tells Django to reuse the existing database instead of trying to create a new one.
   ```
   pytest --keepdb
   ```

2. **Grant CREATEDB permission to your database user**: If you have admin access to PostgreSQL, you can grant the CREATEDB permission to your user:
   ```sql
   ALTER USER triumph_user WITH CREATEDB;
   ```

### Running All Tests

To run all tests:

```bash
cd e-com-2024-apis
# Use the provided scripts
./run_tests.sh  # On Linux/Mac
run_tests.bat   # On Windows

# Or run pytest directly
pytest --keepdb
```

### Running Specific Test Categories

To run specific test categories:

```bash
# Run configuration tests
pytest tests/test_configuration.py --keepdb

# Run payment processing tests
pytest tests/test_payment_processing.py --keepdb

# Run order processing tests
pytest tests/test_order_processing.py --keepdb

# Run email notification tests
pytest tests/test_email_notifications.py --keepdb

# Run stability and robustness tests
pytest tests/test_stability_robustness.py --keepdb

# Run PhonePe service tests
pytest tests/test_phonepe_service.py --keepdb
```

### Running Tests with Verbosity

For more detailed output:

```bash
pytest -v --keepdb
```

### Running Tests with Coverage

To run tests with coverage:

```bash
pip install pytest-cov
pytest --cov=. --keepdb
```

For a detailed HTML coverage report:

```bash
pytest --cov=. --cov-report=html --keepdb
```

## Test Fixtures

The test suite uses fixtures defined in `conftest.py` to set up test data and mock external services. These fixtures include:

- `api_client`: Returns an unauthenticated API client
- `authenticated_client`: Returns an authenticated API client with a regular user
- `admin_client`: Returns an authenticated API client with an admin user
- `create_user`: Factory fixture to create users
- `create_address`: Factory fixture to create addresses
- `create_shipping_method`: Factory fixture to create shipping methods
- `create_product`: Factory fixture to create products
- `create_product_variant`: Factory fixture to create product variants
- `create_order`: Factory fixture to create orders
- `create_payment`: Factory fixture to create payments
- `mock_phonepe_service`: Mock the PhonePe service for testing

## Best Practices

1. **Isolation**: Each test should be independent and not rely on the state created by other tests.
2. **Mocking**: External services (like PhonePe API) should be mocked to avoid making real API calls during testing.
3. **Coverage**: Aim for high test coverage, especially for critical paths like payment processing.
4. **Edge Cases**: Test edge cases and error conditions to ensure the system is robust.
5. **Security**: Include tests for security concerns, especially for payment-related functionality.

## Troubleshooting

If you encounter issues running the tests:

1. Check that your environment variables are set correctly
2. Ensure the database is accessible and has the correct schema
3. Verify that all dependencies are installed
4. Check for any pending migrations that need to be applied
5. If you get database permission errors, use the `--keepdb` flag as described above
6. If tests fail with import errors, make sure you're running the tests from the project root directory

### Common Issues

#### Database Permission Errors

If you see errors like:
```
permission denied to create database
```

Use the `--keepdb` flag as described in the "Database Considerations" section.

#### Email Backend Errors

If you see errors related to email backends, make sure you're using the correct email backend in your test settings. The test suite is configured to use the console backend by default.

For more help, contact the development team.
