# from rest_framework import viewsets, generics, status, permissions
# from rest_framework.decorators import action
# from rest_framework.response import Response
# from rest_framework_simplejwt.views import TokenObtainPairView
# from django.contrib.auth import get_user_model
# from .models import Address, PaymentMethod, Wishlist
# from .serializers import (
#     UserSerializer, UserRegistrationSerializer, AddressSerializer,
#     PaymentMethodSerializer, WishlistSerializer, ChangePasswordSerializer
# )

# User = get_user_model()

# class UserViewSet(viewsets.ModelViewSet):
#     queryset = User.objects.all()
#     serializer_class = UserSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         if self.request.user.is_staff:
#             return User.objects.all()
#         return User.objects.filter(id=self.request.user.id)

#     def get_serializer_class(self):
#         if self.action == 'create':
#             return UserRegistrationSerializer
#         return UserSerializer

#     @action(detail=False, methods=['post'])
#     def change_password(self, request):
#         user = request.user
#         serializer = ChangePasswordSerializer(data=request.data, context={'request': request})

#         if serializer.is_valid():
#             user.set_password(serializer.validated_data['new_password'])
#             user.save()
#             return Response({'message': 'Password changed successfully'})

#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=False, methods=['get'])
#     def profile(self, request):
#         serializer = self.get_serializer(request.user)
#         return Response(serializer.data)

# class AddressViewSet(viewsets.ModelViewSet):
#     serializer_class = AddressSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         return Address.objects.filter(user=self.request.user)

#     @action(detail=False, methods=['get'])
#     def shipping(self, request):
#         addresses = self.get_queryset().filter(address_type='SHIPPING')
#         serializer = self.get_serializer(addresses, many=True)
#         return Response(serializer.data)

#     @action(detail=False, methods=['get'])
#     def billing(self, request):
#         addresses = self.get_queryset().filter(address_type='BILLING')
#         serializer = self.get_serializer(addresses, many=True)
#         return Response(serializer.data)

# class PaymentMethodViewSet(viewsets.ModelViewSet):
#     serializer_class = PaymentMethodSerializer
#     # permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         payment = PaymentMethod.objects.filter(user=1)
#         return payment

#     def perform_create(self, serializer):
#         # Here you would typically integrate with Stripe to create a payment method
#         # and store the stripe_payment_method_id
#         serializer.save(user=self.request.user)

# class WishlistViewSet(viewsets.ModelViewSet):
#     serializer_class = WishlistSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         return Wishlist.objects.filter(user=self.request.user)

#     @action(detail=True, methods=['post'])
#     def toggle(self, request, pk=None):
#         product = self.get_object()
#         wishlist_item = Wishlist.objects.filter(
#             user=request.user,
#             product=product
#         ).first()

#         if wishlist_item:
#             wishlist_item.delete()
#             return Response({'status': 'removed from wishlist'})

#         Wishlist.objects.create(user=request.user, product=product)
#         return Response({'status': 'added to wishlist'})


from rest_framework import generics,  status, permissions, serializers, views
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .models import Address, PaymentMethod, Wishlist, Customer, ContactMessage
from orders.models import Order, OrderItem
from products.models import Product
from users.serializers import (
    UserSerializer,
    UserRegistrationSerializer,
    AddressSerializer,
    PaymentMethodSerializer,
    WishlistSerializer,
    ChangePasswordSerializer,
    SocialLoginSerializer,
    LoginSerializer,
    CustomerProfileSerializer,
    ContactMessageSerializer
)
from users.permissions import IsAuthenticatedOrCreateOnly
from rest_framework_simplejwt.tokens import RefreshToken
from orders.serializers import OrderDetailSerializer
from rest_framework.decorators import api_view, permission_classes
from users.pagination import UserPagination
from django.db.models import Q, Count, Sum, Max
from django.db.models.functions import TruncMonth
from datetime import timedelta
from django.utils import timezone
from .utils import send_contact_emails

User = get_user_model()


class UserListCreateView(generics.ListCreateAPIView):
    queryset = User.objects.all()
    permission_classes = [IsAuthenticatedOrCreateOnly]

    def get_queryset(self):
        if self.request.user.is_staff:
            return User.objects.all()
        return User.objects.filter(id=self.request.user.id)

    def get_serializer_class(self):
        if self.request.method == "POST":
            return UserRegistrationSerializer
        return UserSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate tokens for the new user
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        headers = self.get_success_headers(serializer.data)
        return Response(
            {
                **serializer.data,
                "accessToken": str(access),
                "refreshToken": str(refresh),
            },
            status=status.HTTP_201_CREATED,
            headers=headers,
        )


class SocialLoginView(generics.CreateAPIView):
    serializer_class = SocialLoginSerializer
    queryset = Customer.objects.all()
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data["email"]
        name = serializer.validated_data["name"]
        image_url = serializer.validated_data.get("image_url", "")

        # Log the incoming data for debugging
        print(f"Social login attempt: email={email}, name={name}, provider=Google")

        # Check if the user exists
        user, created = Customer.objects.get_or_create(
            email=email,
            defaults={"name": name, "is_verified": True},
        )

        if created:
            if image_url:
                user.profile_image_url = image_url
            user.save()
            print(f"New user created via Google login: {email}")
        else:
            print(f"Existing user logged in via Google: {email}")
            # Update user information if needed
            if name and user.name != name:
                user.name = name
                user.save()
            if image_url and not user.profile_image_url:
                user.profile_image_url = image_url
                user.save()

        user_json = UserSerializer(user).data
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        response_data = {
            **user_json,
            "accessToken": str(access),
            "refreshToken": str(refresh),
        }

        if created:
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response(response_data, status=status.HTTP_200_OK)


class LoginView(generics.GenericAPIView):
    serializer_class = LoginSerializer
    permission_classes = [
        permissions.AllowAny
    ]  # Allow access to this view without authentication

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data["user"]

        # Generate or retrieve token
        user_json = UserSerializer(user).data
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        return Response(
            {
                **user_json,
                "accessToken": str(access),
                "refreshToken": str(refresh),
            },
            status=status.HTTP_200_OK,
        )


class UserProfileView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class ChangePasswordView(generics.UpdateAPIView):
    serializer_class = ChangePasswordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def update(self, request, *args, **kwargs):
        user = request.user
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            user.set_password(serializer.validated_data["new_password"])
            user.save()
            return Response(
                {"message": "Password changed successfully"}, status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AddressListCreateView(generics.ListCreateAPIView):
    serializer_class = AddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Address.objects.filter(user=self.request.user)


class AddressDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = AddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Address.objects.filter(user=self.request.user)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance_id = instance.id
        self.perform_destroy(instance)
        return Response({"id": instance_id}, status=status.HTTP_200_OK)


class PaymentMethodListCreateView(generics.ListCreateAPIView):
    serializer_class = PaymentMethodSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return PaymentMethod.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class WishlistListCreateView(generics.ListCreateAPIView):
    serializer_class = WishlistSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Wishlist.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        product_id = self.request.data.get("product_id")
        if not product_id:
            raise serializers.ValidationError({"error": "Product ID is required"})

        if Wishlist.objects.filter(
            product_id=product_id, user=self.request.user
        ).exists():
            raise serializers.ValidationError({"error": "Product already exists"})
        serializer.save(user=self.request.user, product_id=product_id)


class WishlistToggleView(generics.GenericAPIView):
    serializer_class = WishlistSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        product = self.get_object()
        wishlist_item = Wishlist.objects.filter(
            user=request.user, product=product
        ).first()

        if wishlist_item:
            wishlist_item.delete()
            return Response(
                {"status": "removed from wishlist"}, status=status.HTTP_200_OK
            )

        Wishlist.objects.create(user=request.user, product=product)
        return Response({"status": "added to wishlist"}, status=status.HTTP_201_CREATED)


@api_view(["DELETE"])
@permission_classes([permissions.IsAuthenticated])
def remove_wishlist(request):
    id = request.query_params.get("wishlist_id", None)
    if id:
        wishlist = Wishlist.objects.filter(id=id)
        if not wishlist:
            wishlist = Wishlist.objects.filter(product_id=id)
        if not wishlist:
            return Response({"status": "wishlist not found"}, status=status.HTTP_404_NOT_FOUND)
        wishlist.delete()
    return Response({"status": "removed from wishlist"}, status=status.HTTP_200_OK)


# class UserDetailView(generics.ListAPIView):
#     serializer_class = UserDetailSerilizer
#     permission_classes = [permissions.IsAuthenticated]


#     def get_queryset(self):
#         return Order.objects.filter(user=self.request.user)


#     def get_user(self):
#         return User.objects.filter(id=self.request.user.id)
class UserDetailView(generics.ListAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Get user details
        user = User.objects.get(id=self.request.user.id)

        address = Address.objects.filter(user=user)
        shipping_address = AddressSerializer(address, many=True)
        payment_methods = PaymentMethod.objects.filter(user=user)
        payment_methods_serializer = PaymentMethodSerializer(payment_methods, many=True)
        wishlist = Wishlist.objects.filter(user=user)
        wishlist_serializer = WishlistSerializer(wishlist, many=True)
        orders = Order.objects.filter(user=user)
        order_serializer = OrderDetailSerializer(orders, many=True)

        # # If there are no orders, return user details
        # if not orders.exists():
        #     user_data = UserSerializer(user).data
        #     return Response(
        #         {
        #             "user": user_data,
        #             "orders": [],
        #             "shipping_address": shipping_address.data,
        #         }
        #     )
        # if not address.exists() or not orders.exists():
        #     return Response({"user": user_data, "orders": [], "shipping_address": []})

        # If there are orders, serialize them
        return Response(
            {
                "user": UserSerializer(user).data,
                "orders": order_serializer.data,
                "shipping_address": shipping_address.data,
                "payment_methods": payment_methods_serializer.data,
                "wishlist": wishlist_serializer.data,
            }
        )

class CustomerProfileUpdateView(generics.UpdateAPIView):
    queryset = Customer.objects.all()
    serializer_class = CustomerProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        # Ensure users can only update their own profile
        return self.request.user


class AdminLoginView(generics.GenericAPIView):
    serializer_class = LoginSerializer
    permission_classes = [
        permissions.AllowAny
    ]  # Allow access to this view without authentication

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data["user"]

        # Generate or retrieve token
        user_json = UserSerializer(user).data

        if not user.is_superuser:
            return Response(
                {"error": "unauthorized", "message": "login with admin credentials"},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        return Response(
            {
                **user_json,
                "accessToken": str(access),
                "refreshToken": str(refresh),
            },
            status=status.HTTP_200_OK,
        )


class CustomerListView(generics.ListAPIView):
    permission_classes = [permissions.IsAdminUser]
    pagination_class = UserPagination

    def get(self, request, *args, **kwargs):
        # Get search query parameter
        search = self.request.query_params.get("search")
        q_filters = Q(order__isnull=False)  # Users with at least one order

        if search:
            # Apply search filters on name, phone_number, and email fields
            q_filters &= (
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(
                    phone_number__icontains=search
                )  # Adjust if phone_number is in a related profile model
            )

        # Retrieve users based on the combined filters
        users_with_orders = User.objects.filter(q_filters).annotate(
            order_count=Count("order", distinct=True),
            total_orders=Sum("order__total"),
            last_order_date=Max('order__created_at')
        )

        # Apply pagination to the user queryset
        paginator = self.pagination_class()
        paginated_users = paginator.paginate_queryset(users_with_orders, request)

        # Retrieve orders, addresses, payment methods, and wishlists for paginated users
        orders = Order.objects.filter(user__in=paginated_users)
        addresses = Address.objects.filter(user__in=paginated_users)
        payment_methods = PaymentMethod.objects.filter(user__in=paginated_users)
        wishlist = Wishlist.objects.filter(user__in=paginated_users)

        # Serialize data
        user_serializer = UserSerializer(paginated_users, many=True)
        order_serializer = OrderDetailSerializer(orders, many=True)
        shipping_address_serializer = AddressSerializer(addresses, many=True)
        payment_methods_serializer = PaymentMethodSerializer(payment_methods, many=True)
        wishlist_serializer = WishlistSerializer(wishlist, many=True)

        # Return the paginated response
        return paginator.get_paginated_response(
            {
                "users": user_serializer.data,
                "orders": order_serializer.data,
                "shipping_address": shipping_address_serializer.data,
                "payment_methods": payment_methods_serializer.data,
                "wishlist": wishlist_serializer.data,
            }
        )


class StatisticsView(views.APIView):
    def get(self, request, *args, **kwargs):
        # Current date
        now = timezone.now()

        # Calculate date ranges
        last_30_days_start = now - timedelta(days=30)
        previous_30_days_start = last_30_days_start - timedelta(days=30)
        previous_30_days_end = last_30_days_start

        # Helper function to calculate percentage delta
        def calculate_percentage_delta(current, previous):
            if previous == 0:
                return None  # Or set to 0 or another value if you prefer
            return int(((current - previous) / previous) * 100)

        # 1. Total Revenue
        total_revenue_last_30_days = Order.objects.filter(
            created_at__gte=last_30_days_start
        ).aggregate(total_revenue=Sum('total'))['total_revenue'] or 0

        total_revenue_previous_30_days = Order.objects.filter(
            created_at__gte=previous_30_days_start,
            created_at__lt=previous_30_days_end
        ).aggregate(total_revenue=Sum('total'))['total_revenue'] or 0

        revenue_delta = calculate_percentage_delta(total_revenue_last_30_days, total_revenue_previous_30_days)

        # 2. New Customers
        new_customers_last_30_days = User.objects.filter(
            date_joined__gte=last_30_days_start,
            order__isnull=False
        ).distinct().count()

        new_customers_previous_30_days = User.objects.filter(
            date_joined__gte=previous_30_days_start,
            date_joined__lt=previous_30_days_end,
            order__isnull=False
        ).distinct().count()

        new_customers_delta = calculate_percentage_delta(new_customers_last_30_days, new_customers_previous_30_days)

        # 3. Total Orders
        total_orders_last_30_days = Order.objects.filter(
            created_at__gte=last_30_days_start
        ).count()

        total_orders_previous_30_days = Order.objects.filter(
            created_at__gte=previous_30_days_start,
            created_at__lt=previous_30_days_end
        ).count()

        total_orders_delta = calculate_percentage_delta(total_orders_last_30_days, total_orders_previous_30_days)

        # 4. Active Products (unchanged)
        active_products = Product.objects.filter(is_active=True).count()

        # Return the response
        return Response({
            'last_30_days': {
                'total_revenue': total_revenue_last_30_days,
                'new_customers': new_customers_last_30_days,
                'total_orders': total_orders_last_30_days,
            },
            'previous_30_days': {
                'total_revenue': total_revenue_previous_30_days,
                'new_customers': new_customers_previous_30_days,
                'total_orders': total_orders_previous_30_days,
            },
            'percentage_delta': {
                'revenue_delta': revenue_delta,
                'new_customers_delta': new_customers_delta,
                'total_orders_delta': total_orders_delta,
            },
            'active_products': active_products,
        })

class GraphDataView(views.APIView):
    def get(self, request, *args, **kwargs):
        # Define the time range (e.g., last 6 months for sales data)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=180)  # Last 6 months

        # 1. Revenue Over Time (Monthly Revenue for the Last 6 Months)
        revenue_over_time = (
            Order.objects.filter(created_at__range=(start_date, end_date))
            .annotate(month=TruncMonth('created_at'))  # Truncate to month
            .values('month')
            .annotate(monthly_revenue=Sum('total'))
            .order_by('month')
        )

        # Format revenue data for the graph
        sales_data = [
            {"name": self.get_month_name(item['month'].month), "value": item['monthly_revenue']}
            for item in revenue_over_time
        ]

        # 2. Top Products (by revenue in the last 6 months)
        top_products = (
            OrderItem.objects.filter(order__created_at__range=(start_date, end_date))
            .values('product__name')
            .annotate(total_revenue=Sum('total_price'))
            .order_by('-total_revenue')[:10]  # Top 10 products by revenue
        )

        # Format product data for the graph
        product_data = [
            {"name": item['product__name'], "sales": item['total_revenue']}
            for item in top_products
        ]

        # Return the response
        return Response({
            'salesData': sales_data,
            'productData': product_data,
        })

    @staticmethod
    def get_month_name(month_number):
        months = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ]
        return months[month_number - 1] if 1 <= month_number <= 12 else "Unknown"


class ContactFormView(generics.CreateAPIView):
    """
    API view to handle contact form submissions.
    Saves the contact message to the database and sends confirmation emails.
    """
    serializer_class = ContactMessageSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Save the contact message to the database
        contact_message = serializer.save()

        # Send confirmation emails to both sender and receiver
        email_sent = send_contact_emails(serializer.validated_data)

        if email_sent:
            return Response(
                {
                    "success": True,
                    "message": "Your message has been sent successfully. We'll get back to you soon.",
                    "data": serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        else:
            # Even if email sending fails, the message is still saved in the database
            return Response(
                {
                    "success": True,
                    "message": "Your message has been received, but there was an issue sending the confirmation email. We'll still get back to you soon.",
                    "data": serializer.data
                },
                status=status.HTTP_201_CREATED
            )