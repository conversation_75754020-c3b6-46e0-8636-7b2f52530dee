# MinIO Setup for Media Storage

This document explains how to set up MinIO for media storage in the e-commerce project.

## What is MinIO?

MinIO is a high-performance, distributed object storage system. It is software-defined, runs on industry-standard hardware, and is 100% open source. MinIO is compatible with Amazon S3 API, making it a great alternative for storing media files.

## Why Use MinIO?

- **Scalable**: Min<PERSON> can scale to handle large amounts of data
- **S3 Compatible**: Uses the same API as Amazon S3, making it easy to switch between them
- **Fast**: High-performance object storage
- **Secure**: Supports encryption and access control
- **Easy to Deploy**: Can be deployed as a Docker container

## Setup Instructions

### Local Development

1. **Start MinIO using Docker Compose**:

   ```bash
   docker-compose -f docker-compose.minio.yml up -d minio minio-setup
   ```

2. **Access MinIO Console**:
   
   Open your browser and navigate to http://localhost:9001
   
   - Username: `minioadmin`
   - Password: `minioadmin`

3. **Configure Django Settings**:

   The project is already configured to use MinIO when the `USE_MINIO` environment variable is set to `true`. Make sure the following environment variables are set:

   ```
   USE_MINIO=true
   MINIO_STORAGE_ENDPOINT=localhost:9000
   MINIO_STORAGE_ACCESS_KEY=minioadmin
   MINIO_STORAGE_SECRET_KEY=minioadmin
   MINIO_STORAGE_USE_HTTPS=false
   MINIO_STORAGE_MEDIA_BUCKET_NAME=media
   MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET=true
   ```

4. **Sync Existing Media Files**:

   To sync existing media files to MinIO, run:

   ```bash
   python sync_media_to_minio.py --media-dir media --force
   ```

### Production Deployment with Coolify

1. **Set up MinIO Service in Coolify**:

   - In Coolify dashboard, go to "Services" and add a new MinIO service
   - Configure the service with the following settings:
     - Username: Choose a secure username
     - Password: Choose a secure password
     - Expose port 9000 for API access
     - Expose port 9001 for Console access (optional)

2. **Configure Environment Variables**:

   In your application deployment settings in Coolify, add the following environment variables:

   ```
   USE_MINIO=true
   MINIO_STORAGE_ENDPOINT=minio:9000
   MINIO_STORAGE_ACCESS_KEY=your_minio_username
   MINIO_STORAGE_SECRET_KEY=your_minio_password
   MINIO_STORAGE_USE_HTTPS=false
   MINIO_STORAGE_MEDIA_BUCKET_NAME=media
   MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET=true
   ```

3. **Deploy Your Application**:

   When you deploy your application, the build process will automatically:
   
   - Install the required dependencies
   - Create the media bucket if it doesn't exist
   - Sync media files to MinIO

## Updating the Next.js Frontend

Update your Next.js frontend to use the MinIO URLs for media files. In your `next.config.ts` file, add the MinIO domain to the `remotePatterns` array:

```typescript
{
  protocol: "http",
  hostname: "your-minio-domain-or-ip",
  port: "9000",
  pathname: "/media/**",
},
```

## Troubleshooting

- **Connection Issues**: Make sure the MinIO service is running and accessible from your application
- **Permission Denied**: Check that the access key and secret key are correct
- **Bucket Not Found**: Ensure the bucket exists or set `MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET` to `true`
- **File Not Found**: Check that the file path is correct and the file exists in MinIO

## Additional Resources

- [MinIO Documentation](https://min.io/docs/minio/container/index.html)
- [django-minio-storage Documentation](https://django-minio-storage.readthedocs.io/)
- [Coolify MinIO Service](https://coolify.io/docs/services/minio)
