import os
import sys
import pytest
from django.conf import settings
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from decimal import Decimal

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment - use test settings for testing
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tests.test_settings')

# Import Django models after setting up the environment
from orders.models import Order, Payment, ShippingMethod
from users.models import Address
from products.models import Product, ProductVariant

User = get_user_model()

@pytest.fixture
def api_client():
    """Return an authenticated API client."""
    return APIClient()

@pytest.fixture
def create_user():
    """Factory fixture to create users."""
    def _create_user(email='<EMAIL>', password='testpassword', is_staff=False):
        user = User.objects.create_user(
            email=email,
            password=password,
            name='Test User',
            phone_number='1234567890',
            is_staff=is_staff
        )
        return user
    return _create_user

@pytest.fixture
def authenticated_client(api_client, create_user):
    """Return an authenticated API client."""
    user = create_user()
    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client, user

@pytest.fixture
def admin_client(api_client, create_user):
    """Return an authenticated admin API client."""
    admin_user = create_user(email='<EMAIL>', is_staff=True)
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client, admin_user

@pytest.fixture
def create_address():
    """Factory fixture to create addresses."""
    def _create_address(user, is_default=True):
        address = Address.objects.create(
            user=user,
            address_type='SHIPPING',
            street_address='123 Test Street',
            apartment='Apt 456',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country',
            order_user_phone=user.phone_number,
            order_user_email=user.email,
            is_default=is_default
        )
        return address
    return _create_address

@pytest.fixture
def create_shipping_method():
    """Factory fixture to create shipping methods."""
    def _create_shipping_method(name='Standard Shipping', price=Decimal('10.00')):
        shipping_method = ShippingMethod.objects.create(
            name=name,
            description=f'{name} description',
            price=price,
            estimated_days=3,
            is_active=True
        )
        return shipping_method
    return _create_shipping_method

@pytest.fixture
def create_product():
    """Factory fixture to create products."""
    def _create_product(name='Test Product', price=Decimal('100.00')):
        product = Product.objects.create(
            name=name,
            description=f'{name} description',
            price=price,
            stock=10,
            is_active=True
        )
        return product
    return _create_product

@pytest.fixture
def create_product_variant():
    """Factory fixture to create product variants."""
    def _create_product_variant(product, name='Test Variant', price=None):
        variant = ProductVariant.objects.create(
            product=product,
            name=name,
            price=price or product.price,
            stock=5
        )
        return variant
    return _create_product_variant

@pytest.fixture
def create_order():
    """Factory fixture to create orders."""
    def _create_order(user, shipping_address, billing_address, shipping_method, total=Decimal('200.00')):
        order = Order.objects.create(
            user=user,
            shipping_address=shipping_address,
            billing_address=billing_address,
            shipping_method=shipping_method,
            subtotal=total - shipping_method.price,
            shipping_cost=shipping_method.price,
            total=total,
            status='PENDING'
        )
        return order
    return _create_order

@pytest.fixture
def create_payment():
    """Factory fixture to create payments."""
    def _create_payment(order, status='COMPLETED', payment_method='PHONEPE'):
        payment = Payment.objects.create(
            order=order,
            amount=order.total,
            status=status,
            payment_method=payment_method,
            transaction_id=f'TEST_TX_{order.id}'
        )
        return payment
    return _create_payment

@pytest.fixture
def mock_phonepe_service(monkeypatch):
    """Mock the PhonePe service for testing."""
    class MockPhonePeService:
        def __init__(self):
            self.client_id = settings.PHONEPE_CLIENT_ID
            self.client_secret = settings.PHONEPE_CLIENT_SECRET
            self.callback_url = settings.PHONEPE_CALLBACK_URL

        def initiate_payment(self, order, amount):
            transaction_id = f"TEST_TX_{order.id}"
            order.phonepe_transaction_id = transaction_id
            order.phonepe_payment_url = f"https://test.phonepe.com/pay/{transaction_id}"
            order.save()
            return {
                "success": True,
                "transaction_id": transaction_id,
                "payment_url": order.phonepe_payment_url
            }

        def check_payment_status(self, transaction_id):
            return {
                "success": True,
                "status": "COMPLETED",
                "details": {
                    "transactionId": transaction_id,
                    "amount": 10000,
                    "status": "COMPLETED"
                }
            }

        def verify_webhook_signature(self, payload, signature):
            """Mock method to verify webhook signatures"""
            # For testing, we'll just return True for a test signature
            return signature == 'test-signature'

        def validate_webhook(self, header_data, response_data):
            """Mock method to validate webhook data"""
            # For testing, we'll parse the response data as JSON
            import json
            try:
                data = json.loads(response_data)
                return {
                    "success": True,
                    "order_id": data.get("merchantTransactionId"),
                    "state": data.get("status"),
                    "details": data
                }
            except:
                return {
                    "success": False,
                    "error": "Invalid webhook data"
                }

    from payment_gateway.services import PhonePeService
    monkeypatch.setattr("payment_gateway.services.PhonePeService", MockPhonePeService)
    return MockPhonePeService()
