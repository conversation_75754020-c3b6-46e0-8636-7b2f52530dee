# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION BEGIN
PassengerAppRoot "/home/<USER>/repositories/e-com-apis"
PassengerBaseURI "/"
PassengerPython "/home/<USER>/virtualenv/repositories/e-com-apis/3.9/bin/python"
# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION END
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION BEGIN
<IfModule Litespeed>
SetEnv NAME wesoswpi_e_commerce
SetEnv USER wesoswpi_e_com_admin
SetEnv PASSWORD Wesolves@2024
SetEnv HOST localhost
SetEnv PORT 5432
</IfModule>
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION END