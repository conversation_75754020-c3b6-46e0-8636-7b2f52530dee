<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Triumph Enterprises</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
        h1 {
            color: #4a5568;
        }
        .order-details {
            background-color: #edf2f7;
            padding: 15px;
            border-left: 4px solid #4a5568;
            margin: 15px 0;
        }
        .payment-status {
            padding: 12px 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .payment-status p {
            margin: 5px 0;
        }
        .payment-status.success {
            background-color: #f0fff4;
            border: 2px solid #48bb78;
            color: #2f855a;
        }
        .payment-status.failure {
            background-color: #fff5f5;
            border: 2px solid #e53e3e;
            color: #c53030;
        }
        .payment-status.pending {
            background-color: #fffaf0;
            border: 2px solid #ed8936;
            color: #c05621;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        .order-items th, .order-items td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            vertical-align: top;
        }
        .order-items th {
            background-color: #4a5568;
            color: white;
            font-size: 11px;
            text-align: center;
        }
        .order-items td:nth-child(2),
        .order-items td:nth-child(3),
        .order-items td:nth-child(4),
        .order-items td:nth-child(5),
        .order-items td:nth-child(6) {
            text-align: center;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            max-width: 400px;
            margin-left: auto;
        }
        .summary-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #eee;
        }
        .summary-table td:last-child {
            text-align: right;
        }
        .total-row {
            background-color: #edf2f7;
            font-weight: bold;
            border-top: 2px solid #4a5568;
        }
        .order-summary {
            margin: 20px 0;
        }
        .shipping-info {
            background-color: #e6f7ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #4a5568;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order Confirmation</h1>
    </div>

    <div class="content">
        <p>Dear {{ order.user.first_name }},</p>

        <p>Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.</p>

        <div class="order-details">
            <p><strong>Order Number:</strong> {{ order.id }}</p>
            <p><strong>Order Date:</strong> {{ order.created_at|date:"F d, Y, g:i a" }}</p>
            <p><strong>Payment Method:</strong> {{ payment_method }}</p>
            <p><strong>Order Status:</strong> {{ order.status }}</p>
        </div>

        {% if payment_status %}
        <div class="payment-status {% if payment_status == 'COMPLETED' %}success{% elif payment_status == 'FAILED' %}failure{% else %}pending{% endif %}">
            <p>Payment Status: {{ payment_status|title }}</p>
            {% if payment_status == 'COMPLETED' and order.status == 'PAID' %}
            <p>Your payment has been successfully processed.</p>
            {% elif payment_status == 'FAILED' or order.status == 'PAYMENT_FAILED' %}
            <p>Your payment could not be processed. Please try again or contact customer support.</p>
            {% else %}
            <p>Your payment is being processed.</p>
            {% endif %}
        </div>
        {% endif %}

        <h3>Order Summary</h3>
        <table class="order-items">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Qty</th>
                    <th>Unit Price</th>
                    <th>Taxable Value</th>
                    <th>GST</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {% for gst_detail in item_gst_details %}
                <tr>
                    <td>
                        <strong>{{ gst_detail.item.product_name }}</strong>
                        {% if gst_detail.item.variant_name %}<br><small>{{ gst_detail.item.variant_name }}</small>{% endif %}
                        <br><small>HSN: {% if gst_detail.item.product.gst.hsn_code %}{{ gst_detail.item.product.gst.hsn_code }}{% else %}8471{% endif %}</small>
                    </td>
                    <td>{{ gst_detail.item.quantity }}</td>
                    <td>₹{{ gst_detail.item.unit_price }}</td>
                    <td>₹{{ gst_detail.total_base_price|floatformat:2 }}</td>
                    <td>
                        {{ gst_detail.gst_rate }}%<br>
                        <small>₹{{ gst_detail.gst_amount|floatformat:2 }}</small>
                    </td>
                    <td>₹{{ gst_detail.total_with_gst|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="order-summary">
            <table class="summary-table">
                <tr>
                    <td><strong>Subtotal (before GST):</strong></td>
                    <td>₹{{ order.subtotal }}</td>
                </tr>
                {% if order.igst_amount and order.igst_amount > 0 %}
                <tr>
                    <td>IGST:</td>
                    <td>₹{{ order.igst_amount|floatformat:2 }}</td>
                </tr>
                {% else %}
                <tr>
                    <td>CGST:</td>
                    <td>₹{% if order.cgst_amount %}{{ order.cgst_amount|floatformat:2 }}{% else %}{% widthratio order.subtotal 100 9 %}{% endif %}</td>
                </tr>
                <tr>
                    <td>SGST:</td>
                    <td>₹{% if order.sgst_amount %}{{ order.sgst_amount|floatformat:2 }}{% else %}{% widthratio order.subtotal 100 9 %}{% endif %}</td>
                </tr>
                {% endif %}
                {% if order.shipping_cost > 0 %}
                <tr>
                    <td>Shipping Charges:</td>
                    <td>₹{{ order.shipping_cost|floatformat:2 }}</td>
                </tr>
                {% endif %}
                <tr class="total-row">
                    <td><strong>Total Amount:</strong></td>
                    <td><strong>₹{{ order.total|floatformat:2 }}</strong></td>
                </tr>
            </table>
        </div>

        <div class="shipping-info">
            <h3>Shipping Information</h3>
            <p><strong>Shipping Method:</strong> {{ order.shipping_method.name }}</p>
            <p><strong>Estimated Delivery:</strong> {{ order.estimated_delivery_date|date:"F d, Y" }}</p>
            <p><strong>Shipping Address:</strong><br>
                {{ order.shipping_address.user.name }}<br>
                {{ order.shipping_address.street_address }}<br>
                {% if order.shipping_address.apartment %}{{ order.shipping_address.apartment }}<br>{% endif %}
                {{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postal_code }}<br>
                {{ order.shipping_address.country }}
            </p>
        </div>

        <div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #4a5568;">
            <h4 style="margin-top: 0; color: #4a5568;">📄 GST Invoice</h4>
            <p style="margin-bottom: 10px;">A GST-compliant invoice has been generated for your order. You can download it from your order details page or it will be attached to your shipping confirmation email.</p>
            <p style="margin-bottom: 0;"><strong>GST Details:</strong> All prices include applicable GST as per Indian tax regulations. CGST and SGST are applicable for intra-state transactions.</p>
        </div>

        <p>We'll send you another email when your order ships. If you have any questions about your order, please contact our customer service team at <a href="mailto:<EMAIL>"><EMAIL></a> or call us at +91 9848486452.</p>

        <p>Thank you for shopping with Triumph Enterprises!</p>

        <p>Best regards,<br>
        Triumph Enterprises Team</p>
    </div>

    <div class="footer">
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>&copy; 2024 Triumph Enterprises. All rights reserved.</p>
        <p>D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri, Patel Nagar, Darussalam, Hyderabad, Telangana 500001, India</p>
    </div>
</body>
</html>
