import os
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = "your-secret-key-here"  # Change this in production

DEBUG = True

ALLOWED_HOSTS = ["*"]

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third party apps
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "drf_yasg",
    "django_filters",
    "minio_storage",
    # Local apps
    "backend",  # Include backend app for management commands
    "users",
    "products",
    "orders",
    "promotions",
    "dashboard",
    "payment_gateway",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.gzip.GZipMiddleware",  # Add GZip compression
]

ROOT_URLCONF = "backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "backend.wsgi.application"
AUTH_USER_MODEL = "users.Customer"

# Use connection pooling in production, regular connection in development
if os.getenv("DEVELOPMENT"):
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.getenv("DB_NAME"),
            "USER": os.getenv("DB_USER"),
            "PASSWORD": os.getenv("DB_PASSWORD"),
            "HOST": os.getenv("DB_HOST"),
            "PORT": os.getenv("DB_PORT"),
        }
    }
else:
    # Try to use connection pooling if available, otherwise fall back to standard PostgreSQL
    try:
        import dj_db_conn_pool.backends.postgresql
        db_engine = "dj_db_conn_pool.backends.postgresql"
        db_options = {
            "POOL_OPTIONS": {
                "POOL_SIZE": 20,
                "MAX_OVERFLOW": 10,
                "RECYCLE": 300,  # Recycle connections after 5 minutes
            }
        }
    except ImportError:
        db_engine = "django.db.backends.postgresql"
        db_options = {}

    DATABASES = {
        "default": {
            "ENGINE": db_engine,
            "NAME": os.getenv("DB_NAME"),
            "USER": os.getenv("DB_USER"),
            "PASSWORD": os.getenv("DB_PASSWORD"),
            "HOST": os.getenv("DB_HOST"),
            "PORT": os.getenv("DB_PORT"),
            **db_options
        }
    }

# Redis Cache Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://127.0.0.1:6379/1")

# Extract Redis components for more reliable connection
REDIS_HOST = os.getenv("REDIS_HOST", "127.0.0.1")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_USERNAME = os.getenv("REDIS_USERNAME", "")
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")

# Always try to use Redis for caching, with fallback to LocMem if Redis is not available
try:
    import redis

    # Test Redis connection - try both URL and direct connection methods
    try:
        # First try with URL
        redis_client = redis.from_url(REDIS_URL, socket_timeout=2.0)
        redis_client.ping()
    except (redis.exceptions.ConnectionError, redis.exceptions.AuthenticationError):
        # If URL fails, try with direct connection parameters
        if REDIS_USERNAME:
            redis_client = redis.Redis(
                host=REDIS_HOST,
                port=int(REDIS_PORT),
                db=int(REDIS_DB),
                username=REDIS_USERNAME,
                password=REDIS_PASSWORD,
                socket_timeout=2.0
            )
        else:
            redis_client = redis.Redis(
                host=REDIS_HOST,
                port=int(REDIS_PORT),
                db=int(REDIS_DB),
                password=REDIS_PASSWORD if REDIS_PASSWORD else None,
                socket_timeout=2.0
            )
        redis_client.ping()  # Will raise an exception if connection fails

    # Redis is available, use it for caching
    # Determine the best connection string format based on authentication
    if REDIS_USERNAME:
        # Redis 6+ with ACL username/password authentication
        redis_location = f"redis://{REDIS_USERNAME}:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }
    elif REDIS_PASSWORD:
        # Redis with password authentication only
        redis_location = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }
    else:
        # Redis without authentication
        redis_location = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }

    print(f"Using Redis cache with location: {redis_location}")

    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": redis_location,
            "OPTIONS": redis_options,
            "KEY_PREFIX": "ecom",
            "TIMEOUT": 60 * 60 * 24,  # 1 day default timeout
        }
    }

    # Use Redis for session storage
    SESSION_ENGINE = "django.contrib.sessions.backends.cache"
    SESSION_CACHE_ALIAS = "default"

    print("Redis cache configured successfully")

except (ImportError, redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
    # Redis is not available, fallback to LocMem
    print(f"Redis connection failed, falling back to LocMemCache: {str(e)}")
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "unique-snowflake",
        }
    }


AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Import MinIO settings
from .minio_settings import *

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "staticfiles"),
]

# Media settings - these will be overridden by MinIO settings when USE_MINIO is True
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "DEFAULT_FILTER_BACKENDS": (
        "django_filters.rest_framework.DjangoFilterBackend",
        # 'rest_framework.filters.CharFilter',
        "rest_framework.filters.OrderingFilter",
        "rest_framework.filters.SearchFilter",
    ),
}

# JWT settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=30),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": True,
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:8080",
    "http://localhost:3001",
    "https://e-com-2024-6gg4jrnpj-wesolvestechgmailcoms-projects.vercel.app",
    "https://e-com-2024.vercel.app",
    "https://trio.net.in",
    "https://dev.trio.net.in",
]

# Custom user model

# Stripe settings
STRIPE_PUBLIC_KEY = "your-stripe-public-key"
STRIPE_SECRET_KEY = "your-stripe-secret-key"

# PhonePe settings
PHONEPE_CLIENT_ID = os.getenv("PHONEPE_CLIENT_ID")
PHONEPE_CLIENT_SECRET = os.getenv("PHONEPE_CLIENT_SECRET")
PHONEPE_CALLBACK_URL = os.getenv("PHONEPE_CALLBACK_URL")
PHONEPE_ENVIRONMENT = os.getenv("PHONEPE_ENVIRONMENT", "UAT")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")

# Google OAuth settings
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")

TIME_ZONE = "Asia/Kolkata"

CSRF_TRUSTED_ORIGINS = [
    os.getenv("CSRF_ORIGINS"),
]

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.hostinger.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 465))
# For Hostinger with port 465, we need to use SSL, not TLS
EMAIL_USE_SSL = os.getenv('EMAIL_SSL', 'True').lower() == 'true'
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'False').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Email timeout settings to prevent long hanging connections
EMAIL_TIMEOUT = 30  # 30 seconds timeout
