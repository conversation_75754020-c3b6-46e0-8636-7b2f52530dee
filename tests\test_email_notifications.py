import pytest
from unittest.mock import patch, MagicMock
from django.conf import settings
from orders.utils import (
    send_email,
    send_order_confirmation_email,
    send_payment_success_email,
    send_payment_failure_email
)
from users.utils import send_notification_email

class TestEmailNotifications:
    """Tests for email notifications."""

    @patch('orders.utils.EmailMultiAlternatives')
    def test_send_email(self, mock_email):
        """Test sending an email."""
        # Setup mock
        mock_instance = MagicMock()
        mock_email.return_value = mock_instance

        # Call the function
        result = send_email(
            subject='Test Subject',
            template_name='emails/order_confirmation.html',
            context={'key': 'value'},
            to_email='<EMAIL>'
        )

        # Assertions
        assert result is True
        mock_email.assert_called_once()
        mock_instance.attach_alternative.assert_called_once()
        mock_instance.send.assert_called_once()

    @patch('orders.utils.send_email')
    @pytest.mark.django_db
    def test_send_order_confirmation_email(self, mock_send_email, create_user, create_address, create_shipping_method, create_order, create_payment):
        """Test sending order confirmation email."""
        # Setup mocks
        mock_send_email.return_value = True

        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)
        payment = create_payment(order)

        # Call the function
        result = send_order_confirmation_email(order, payment)

        # Assertions
        assert result is True
        assert mock_send_email.call_count == 2  # One for customer, one for admin

        # Check customer email
        customer_call = mock_send_email.call_args_list[0]
        assert 'Order Confirmation' in customer_call[1]['subject']
        assert customer_call[1]['to_email'] == user.email

        # Check admin email
        admin_call = mock_send_email.call_args_list[1]
        assert 'New Order Notification' in admin_call[1]['subject']
        # Check that both emails are included
        expected_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
        assert admin_call[1]['to_email'] == expected_emails

    @patch('orders.utils.send_email')
    @pytest.mark.django_db
    def test_send_payment_success_email(self, mock_send_email, create_user, create_address, create_shipping_method, create_order, create_payment):
        """Test sending payment success email."""
        # Setup mocks
        mock_send_email.return_value = True

        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)
        payment = create_payment(order)

        # Call the function
        result = send_payment_success_email(order, payment)

        # Assertions
        assert result is True
        assert mock_send_email.call_count == 2  # One for customer, one for admin

        # Check customer email
        customer_call = mock_send_email.call_args_list[0]
        assert 'Payment Successful' in customer_call[1]['subject']
        assert customer_call[1]['to_email'] == user.email

        # Check admin email
        admin_call = mock_send_email.call_args_list[1]
        assert 'Payment Success Notification' in admin_call[1]['subject']
        # Check that both emails are included
        expected_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
        assert admin_call[1]['to_email'] == expected_emails

    @patch('orders.utils.send_email')
    @pytest.mark.django_db
    def test_send_payment_failure_email(self, mock_send_email, create_user, create_address, create_shipping_method, create_order, create_payment):
        """Test sending payment failure email."""
        # Setup mocks
        mock_send_email.return_value = True

        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)
        payment = create_payment(order, status='FAILED')

        # Call the function
        result = send_payment_failure_email(order, payment)

        # Assertions
        assert result is True
        assert mock_send_email.call_count == 2  # One for customer, one for admin

        # Check customer email
        customer_call = mock_send_email.call_args_list[0]
        assert 'Payment Failed' in customer_call[1]['subject']
        assert customer_call[1]['to_email'] == user.email

        # Check admin email
        admin_call = mock_send_email.call_args_list[1]
        assert 'Payment Failure Notification' in admin_call[1]['subject']
        # Check that both emails are included
        expected_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
        assert admin_call[1]['to_email'] == expected_emails

    @patch('users.utils.EmailMultiAlternatives')
    def test_contact_notification_email(self, mock_email):
        """Test sending contact notification email."""
        # Setup mock
        mock_instance = MagicMock()
        mock_email.return_value = mock_instance

        # Create test data
        contact_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'subject': 'Test Subject',
            'message': 'This is a test message',
            'date': '2024-01-01 12:00 PM'
        }

        # Call the function
        send_notification_email(contact_data)

        # Assertions
        mock_email.assert_called_once()

        # Check that the email is sent to both addresses
        call_args = mock_email.call_args[0]
        recipients = call_args[3]  # The recipient list is the 4th argument
        assert len(recipients) == 2
        assert settings.DEFAULT_FROM_EMAIL in recipients
        assert "<EMAIL>" in recipients

        mock_instance.attach_alternative.assert_called_once()
        mock_instance.send.assert_called_once()

    @patch('orders.utils.EmailMultiAlternatives.send')
    @pytest.mark.django_db
    def test_email_error_handling(self, mock_send, create_user, create_address, create_shipping_method, create_order, create_payment):
        """Test error handling in email sending."""
        # Setup mock to raise an exception
        mock_send.side_effect = Exception("SMTP error")

        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)
        payment = create_payment(order)

        # Call the function - should not raise an exception
        result = send_order_confirmation_email(order, payment)

        # Assertions
        assert result is False  # Function should return False on error
