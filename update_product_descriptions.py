#!/usr/bin/env python3
"""
Product Description Update Script

This script updates only the description field in the database using
enhanced descriptions from generated JSON files. It preserves all
other product data and provides comprehensive logging.

Features:
- Updates only product descriptions in PostgreSQL database
- Reads from generated JSON files
- Preserves all other product data
- Comprehensive error handling and logging
- Database connection management using .env configuration
- Backup functionality for rollback

Author: Triumph Enterprise
Created: 2025
"""

import os
import sys
import django
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from django.db import transaction, connection
from products.models import Product
from django.core.exceptions import ObjectDoesNotExist

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('product_description_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ProductDescriptionUpdater:
    """Handles updating product descriptions from JSON files"""
    
    def __init__(self):
        self.updated_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.backup_data = []
    
    def load_json_file(self, filepath: str) -> Dict[str, Any]:
        """Load product data from JSON file"""
        try:
            logger.info(f"Loading product data from: {filepath}")
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise ValueError("JSON file must contain 'products' key")
            
            products = data['products']
            logger.info(f"Loaded {len(products)} products from JSON file")
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading JSON file {filepath}: {str(e)}")
            raise
    
    def create_backup(self, product_ids: List[int]) -> str:
        """Create backup of current descriptions before updating"""
        try:
            logger.info("Creating backup of current product descriptions...")
            
            # Get current descriptions
            products = Product.objects.filter(id__in=product_ids).values(
                'id', 'name', 'description', 'updated_at'
            )
            
            backup_data = {
                "backup_created_at": datetime.now().isoformat(),
                "total_products": len(products),
                "products": list(products)
            }
            
            # Save backup file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"description_backup_{timestamp}.json"
            backup_filepath = os.path.join(
                os.path.dirname(__file__), 
                "data new qubo jsons", 
                backup_filename
            )
            
            os.makedirs(os.path.dirname(backup_filepath), exist_ok=True)
            
            with open(backup_filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Backup created: {backup_filepath}")
            return backup_filepath
            
        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            raise
    
    def update_product_description(self, product_data: Dict[str, Any]) -> bool:
        """Update a single product's description"""
        try:
            product_id = product_data.get('id')
            enhanced_description = product_data.get('enhanced_description')
            
            if not product_id or not enhanced_description:
                logger.warning(f"Missing required data for product: {product_data.get('name', 'Unknown')}")
                return False
            
            # Get the product
            try:
                product = Product.objects.get(id=product_id)
            except ObjectDoesNotExist:
                logger.warning(f"Product with ID {product_id} not found in database")
                return False
            
            # Store backup data
            self.backup_data.append({
                'id': product.id,
                'name': product.name,
                'old_description': product.description,
                'new_description': enhanced_description
            })
            
            # Update description
            old_description = product.description
            product.description = enhanced_description
            product.save(update_fields=['description', 'updated_at'])
            
            logger.info(f"Updated description for product: {product.name} (ID: {product_id})")
            logger.debug(f"Old description length: {len(old_description or '')}")
            logger.debug(f"New description length: {len(enhanced_description)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating product {product_data.get('name', 'Unknown')}: {str(e)}")
            return False
    
    def update_descriptions_from_json(self, json_filepath: str, dry_run: bool = False) -> Dict[str, int]:
        """Update product descriptions from JSON file"""
        try:
            # Load JSON data
            json_data = self.load_json_file(json_filepath)
            products = json_data['products']
            
            if dry_run:
                logger.info("DRY RUN MODE - No actual updates will be performed")
            
            # Get product IDs for backup
            product_ids = [p.get('id') for p in products if p.get('id')]
            
            # Create backup if not dry run
            backup_filepath = None
            if not dry_run and product_ids:
                backup_filepath = self.create_backup(product_ids)
            
            # Reset counters
            self.updated_count = 0
            self.failed_count = 0
            self.skipped_count = 0
            
            # Process products in batches
            batch_size = 50
            total_products = len(products)
            
            logger.info(f"Starting to process {total_products} products...")
            
            with transaction.atomic():
                for i in range(0, total_products, batch_size):
                    batch = products[i:i + batch_size]
                    logger.info(f"Processing batch {i//batch_size + 1} ({len(batch)} products)")
                    
                    for product_data in batch:
                        try:
                            if dry_run:
                                # Just validate data in dry run
                                product_id = product_data.get('id')
                                enhanced_description = product_data.get('enhanced_description')
                                
                                if product_id and enhanced_description:
                                    if Product.objects.filter(id=product_id).exists():
                                        self.updated_count += 1
                                        logger.debug(f"DRY RUN: Would update product ID {product_id}")
                                    else:
                                        self.skipped_count += 1
                                        logger.debug(f"DRY RUN: Product ID {product_id} not found")
                                else:
                                    self.failed_count += 1
                                    logger.debug(f"DRY RUN: Invalid data for product")
                            else:
                                # Actual update
                                if self.update_product_description(product_data):
                                    self.updated_count += 1
                                else:
                                    self.failed_count += 1
                                    
                        except Exception as e:
                            logger.error(f"Error processing product in batch: {str(e)}")
                            self.failed_count += 1
                            continue
                    
                    # Log progress
                    progress = ((i + len(batch)) / total_products) * 100
                    logger.info(f"Progress: {progress:.1f}% ({i + len(batch)}/{total_products})")
            
            # Prepare results
            results = {
                'total_processed': total_products,
                'updated': self.updated_count,
                'failed': self.failed_count,
                'skipped': self.skipped_count,
                'backup_file': backup_filepath
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Error updating descriptions from JSON: {str(e)}")
            raise
    
    def find_latest_json_file(self, directory: str) -> Optional[str]:
        """Find the latest enhanced products JSON file"""
        try:
            json_dir = Path(directory)
            if not json_dir.exists():
                logger.error(f"Directory not found: {directory}")
                return None
            
            # Look for enhanced products JSON files
            json_files = list(json_dir.glob("enhanced_products_*.json"))
            
            if not json_files:
                logger.error(f"No enhanced products JSON files found in {directory}")
                return None
            
            # Sort by modification time and get the latest
            latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"Found latest JSON file: {latest_file}")
            
            return str(latest_file)
            
        except Exception as e:
            logger.error(f"Error finding latest JSON file: {str(e)}")
            return None
    
    def validate_database_connection(self) -> bool:
        """Validate database connection"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    logger.info("Database connection validated successfully")
                    return True
                else:
                    logger.error("Database connection validation failed")
                    return False
        except Exception as e:
            logger.error(f"Database connection error: {str(e)}")
            return False

def main():
    """Main function to run the description update"""
    try:
        logger.info("Starting product description update script...")
        
        # Validate database connection
        updater = ProductDescriptionUpdater()
        if not updater.validate_database_connection():
            logger.error("Cannot proceed without valid database connection")
            return
        
        # Find the latest JSON file
        json_directory = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        json_filepath = updater.find_latest_json_file(json_directory)
        
        if not json_filepath:
            logger.error("No JSON file found to process")
            return
        
        # Ask user for confirmation
        print(f"\nFound JSON file: {json_filepath}")
        print("This will update product descriptions in the database.")
        
        # Perform dry run first
        print("\nPerforming dry run to validate data...")
        dry_run_results = updater.update_descriptions_from_json(json_filepath, dry_run=True)
        
        print(f"\nDry run results:")
        print(f"- Total products: {dry_run_results['total_processed']}")
        print(f"- Would update: {dry_run_results['updated']}")
        print(f"- Would skip: {dry_run_results['skipped']}")
        print(f"- Would fail: {dry_run_results['failed']}")
        
        if dry_run_results['failed'] > 0:
            print(f"\nWarning: {dry_run_results['failed']} products have issues!")
        
        # Ask for confirmation
        response = input("\nProceed with actual update? (y/N): ").strip().lower()
        if response != 'y':
            print("Update cancelled by user")
            return
        
        # Perform actual update
        print("\nPerforming actual update...")
        results = updater.update_descriptions_from_json(json_filepath, dry_run=False)
        
        # Print summary
        print("\n" + "="*60)
        print("PRODUCT DESCRIPTION UPDATE SUMMARY")
        print("="*60)
        print(f"Total products processed: {results['total_processed']}")
        print(f"Successfully updated: {results['updated']}")
        print(f"Failed updates: {results['failed']}")
        print(f"Skipped: {results['skipped']}")
        if results['backup_file']:
            print(f"Backup file: {results['backup_file']}")
        print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        logger.info("Product description update completed successfully!")
        
    except Exception as e:
        logger.error(f"Script execution failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
