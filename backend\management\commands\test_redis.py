from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
import time
import redis

class Command(BaseCommand):
    help = 'Test Redis connection and cache operations'

    def handle(self, *args, **options):
        self.stdout.write(self.style.NOTICE('Testing Redis connection and cache operations...'))
        
        # Get Redis URL from settings
        redis_url = getattr(settings, 'REDIS_URL', 'redis://127.0.0.1:6379/1')
        self.stdout.write(f"Redis URL: {redis_url}")
        
        # Test direct Redis connection
        self._test_direct_connection(redis_url)
        
        # Test Django cache operations
        self._test_django_cache()
        
    def _test_direct_connection(self, redis_url):
        """Test direct connection to Redis using redis-py."""
        self.stdout.write(self.style.NOTICE('\nTesting direct Redis connection...'))
        
        try:
            # Create Redis client with a short timeout
            redis_client = redis.from_url(redis_url, socket_timeout=5.0)
            
            # Test connection with PING
            response = redis_client.ping()
            if response:
                self.stdout.write(self.style.SUCCESS('✅ Direct Redis connection successful!'))
                
                # Test basic operations
                self.stdout.write('Testing basic Redis operations...')
                
                # Set a test key
                redis_client.set('test_direct_key', 'test_value')
                
                # Get the test key
                value = redis_client.get('test_direct_key')
                if value and value.decode() == 'test_value':
                    self.stdout.write(self.style.SUCCESS('✅ Redis SET/GET operations successful!'))
                else:
                    self.stdout.write(self.style.ERROR('❌ Redis SET/GET operations failed!'))
                    
                # Delete the test key
                redis_client.delete('test_direct_key')
                
                # Check if key was deleted
                if not redis_client.exists('test_direct_key'):
                    self.stdout.write(self.style.SUCCESS('✅ Redis DELETE operation successful!'))
                else:
                    self.stdout.write(self.style.ERROR('❌ Redis DELETE operation failed!'))
        except redis.exceptions.ConnectionError as e:
            self.stdout.write(self.style.ERROR(f'❌ Redis connection error: {str(e)}'))
        except redis.exceptions.TimeoutError as e:
            self.stdout.write(self.style.ERROR(f'❌ Redis connection timeout: {str(e)}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Unexpected error: {str(e)}'))
    
    def _test_django_cache(self):
        """Test Django's cache framework with Redis."""
        self.stdout.write(self.style.NOTICE('\nTesting Django cache operations...'))
        
        # Check cache backend
        backend = settings.CACHES['default']['BACKEND']
        self.stdout.write(f"Cache backend: {backend}")
        
        if 'redis' not in backend.lower():
            self.stdout.write(self.style.WARNING('⚠️ Not using Redis as cache backend!'))
        
        try:
            # Test cache set
            test_key = 'django_test_key'
            test_value = f'test_value_{time.time()}'
            
            self.stdout.write(f"Setting cache key '{test_key}' with value '{test_value}'")
            cache.set(test_key, test_value, 60)  # Cache for 60 seconds
            
            # Test cache get
            cached_value = cache.get(test_key)
            if cached_value == test_value:
                self.stdout.write(self.style.SUCCESS(f'✅ Cache GET successful! Retrieved: {cached_value}'))
            else:
                self.stdout.write(self.style.ERROR(f'❌ Cache GET failed! Expected: {test_value}, Got: {cached_value}'))
            
            # Test cache delete
            cache.delete(test_key)
            if cache.get(test_key) is None:
                self.stdout.write(self.style.SUCCESS('✅ Cache DELETE successful!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Cache DELETE failed!'))
                
            # Test cache with timeout
            self.stdout.write('Testing cache timeout...')
            short_key = 'short_timeout_key'
            cache.set(short_key, 'will_expire_soon', 2)  # 2 seconds timeout
            
            self.stdout.write('Waiting for cache to expire (2 seconds)...')
            time.sleep(3)  # Wait for the cache to expire
            
            if cache.get(short_key) is None:
                self.stdout.write(self.style.SUCCESS('✅ Cache timeout working correctly!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Cache timeout not working!'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error testing Django cache: {str(e)}'))
