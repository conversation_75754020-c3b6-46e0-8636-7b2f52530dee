from rest_framework import serializers
from .models import (
    SubCategorie,
    Category,
    Product,
    ProductVariant,
    ProductImage,
    Review,
    Brand,
)
from django.db import transaction
from django.utils.text import slugify
from backend.minio_settings import get_minio_media_url


class SubCategorieSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubCategorie
        fields = ["id", "name", "slug"]
        read_only_fields = ["slug"]


class CategorySerializer(serializers.ModelSerializer):
    subcategories = SubCategorieSerializer(many=True, read_only=True)

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "subcategories"]
        read_only_fields = ["slug"]

    def create(self, validated_data):
        validated_data["slug"] = slugify(validated_data["name"])
        return super().create(validated_data)


class ProductImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ["id", "product", "image", "is_primary", "image_url"]

    def get_image_url(self, obj):
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = ["id", "product", "name", "sku", "price_adjustment", "stock"]


class ReviewSerializer(serializers.ModelSerializer):
    user_email = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = [
            "id",
            "product",
            "user",
            "user_email",
            "rating",
            "title",
            "comment",
            "created_at",
            "is_approved",
        ]
        read_only_fields = ["user", "is_approved"]

    def get_user_email(self, obj):
        return obj.user.email


class ReviewCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = ["product", "rating", "title", "comment"]

    def create(self, validated_data):
        user = self.context["request"].user
        # Check if user has already reviewed this product
        if Review.objects.filter(product=validated_data["product"], user=user).exists():
            raise serializers.ValidationError(
                {"detail": "You have already reviewed this product"}
            )
        return Review.objects.create(user=user, **validated_data)


class ProductSerializer(serializers.ModelSerializer):
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    category_name = serializers.CharField(source="category.name", read_only=True)
    average_rating = serializers.DecimalField(
        max_digits=3, decimal_places=2, read_only=True
    )
    total_reviews = serializers.IntegerField(read_only=True)

    # GST inclusive pricing fields
    mrp = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    base_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    gst_amount = serializers.SerializerMethodField()
    gst_rate = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "category",
            "category_name",
            "price",
            "mrp",
            "base_price",
            "gst_amount",
            "gst_rate",
            "stock",
            "is_active",
            "images",
            "variants",
            "average_rating",
            "total_reviews",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["slug"]

    def get_gst_amount(self, obj):
        """Get GST amount from MRP"""
        return round(obj.calculate_gst_from_mrp(), 2)

    def get_gst_rate(self, obj):
        """Get GST rate percentage"""
        return float(obj.get_gst_rate().rate)

    def create(self, validated_data):
        validated_data["slug"] = slugify(validated_data["name"])
        return super().create(validated_data)


class ProductDetailSerializer(ProductSerializer):
    reviews = ReviewSerializer(many=True, read_only=True)

    class Meta(ProductSerializer.Meta):
        fields = ProductSerializer.Meta.fields + ["reviews"]


class CategoryDetailSerializer(serializers.ModelSerializer):
    products = ProductSerializer(many=True, read_only=True)

    class Meta:
        model = Category
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "parent",
            "image",
            "is_active",
            "products",
        ]


from .models import Category, Product, ProductImage, ProductVariant, Review


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = "__all__"


class ProductImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ["id", "image", "is_primary", "image_url"]

    def get_image_url(self, obj):
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = ["id", "name", "sku", "price_adjustment", "stock"]


class ReviewSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)

    class Meta:
        model = Review
        fields = ["id", "user", "rating", "comment", "created_at"]
        read_only_fields = ["user"]


class SubCategoriesNameOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = SubCategorie
        fields = ["id", "name", "slug"]
        read_only_fields = ["slug"]


class CategoryNameOnlySerializer(serializers.ModelSerializer):
    subcategories = SubCategoriesNameOnlySerializer(many=True, read_only=True)

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "subcategories"]
        read_only_fields = ["slug"]


class BrandNameOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ["id", "name", "image"]


class BrandSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Brand
        fields = ["id", "name", "description", "image", "image_url", "is_active"]

    def get_image_url(self, obj):
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None


class ProductListSerializer(serializers.ModelSerializer):
    category = CategoryNameOnlySerializer(read_only=True)
    brand = serializers.SerializerMethodField()
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(), write_only=True, source="category"
    )
    images = ProductImageSerializer(many=True, read_only=True)
    image = serializers.SerializerMethodField()
    variants = ProductVariantSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()

    # GST inclusive pricing fields
    mrp = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    base_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    gst_amount = serializers.SerializerMethodField()
    gst_rate = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        # Determine if 'GET' request for fields customization
        fields = kwargs.pop("fields", None)
        super().__init__(*args, **kwargs)

        # Dynamically remove or include fields
        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)

    def get_brand(self, obj):
        if obj.brand:
            serializer = BrandSerializer(obj.brand, context=self.context)
            return serializer.data
        return None

    def get_image(self, obj):
        if obj.images.exists():
            image = obj.images.filter(is_primary=True).first()
            if not image:
                image = obj.images.first()
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(image.image.name)
        return None

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "price",
            "mrp",
            "base_price",
            "gst_amount",
            "gst_rate",
            "category",
            "category_id",
            "stock",
            "is_active",
            "images",
            "variants",
            "average_rating",
            "review_count",
            "created_at",
            "updated_at",
            "brand",
            "image",
        ]

    def get_average_rating(self, obj):
        reviews = obj.reviews.all()
        if not reviews:
            return None
        return sum(review.rating for review in reviews) / len(reviews)

    def get_review_count(self, obj):
        return obj.reviews.count()

    def get_gst_amount(self, obj):
        """Get GST amount from MRP"""
        return round(obj.calculate_gst_from_mrp(), 2)

    def get_gst_rate(self, obj):
        """Get GST rate percentage"""
        return float(obj.get_gst_rate().rate)


class ProductDetailSerializer(ProductListSerializer):
    reviews = ReviewSerializer(many=True, read_only=True)
    gst_breakdown = serializers.SerializerMethodField()

    class Meta(ProductListSerializer.Meta):
        fields = ProductListSerializer.Meta.fields + ["reviews", "gst_breakdown"]

    def get_gst_breakdown(self, obj):
        """Get detailed GST breakdown from MRP"""
        return obj.calculate_gst_breakdown_from_mrp(quantity=1, is_inter_state=False)


class CategoryDetailSerializer(serializers.ModelSerializer):
    products = ProductSerializer(many=True, read_only=True)
    children = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "description", "image", "children", "products"]

    def get_children(self, obj):
        return CategorySerializer(obj.children.all(), many=True).data


class ReviewCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = ["product", "rating", "comment"]

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class AdminCategorySerializer(serializers.ModelSerializer):
    # Add product count for each category
    product_count = serializers.SerializerMethodField()
    display_order = serializers.IntegerField(
        source="id"
    )  # Use category ID as display order

    class Meta:
        model = Category
        fields = ["id", "name", "description", "display_order", "product_count"]

    def get_product_count(self, obj):
        # Return the count of products in the category
        return Product.objects.filter(category=obj).count()
