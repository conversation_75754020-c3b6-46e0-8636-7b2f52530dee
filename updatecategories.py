import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from products.models import Category, Product

def create_categories():
    # Define new categories (main categories only)
    categories = [
        "Laminates",
        "Hardware",
        "Furniture fittings",
        "Kitchen Accessories",
        "Kitchen Appliances",
        "Adhesive glue",
        "Tools",
        "Tapes",
        "Locks",
        "Digital Locks",
        "Smart House Accessories"
    ]
    
    # First, clear existing categories
    Category.objects.all().delete()
    
    # Create new categories
    for category_name in categories:
        Category.objects.create(name=category_name)
    
    print("Categories created successfully")

def update_product_categories():
    # Detailed keyword matching based on actual products
    category_rules = {
        'Laminates': ['rehau', 'acrylic', 'solid surface', 'laminate'],
        'Hardware': ['profile', 'track', 'connector', 'handle', 'gola'],
        'Furniture fittings': ['hinge', 'minifix', 'slide', 'drawer', 'telescopic'],
        'Kitchen Accessories': ['cutlery tray', 'dish rack', 'waste bin', 'basket'],
        'Digital Locks': ['smart door lock', 'elite lock', 'essential lock'],
        'Smart House Accessories': ['qubo', 'cam', 'camera', 'wifi', 'smart bulb', 'air purifier']
    }
    
    # Counter for tracking updates
    updates_count = 0
    unmatched_products = []
    
    # Process each product
    for product in Product.objects.all():
        matched = False
        # Safely handle None values in name and description
        name = product.name.lower() if product.name else ""
        description = product.description.lower() if product.description else ""
        product_text = f"{name} {description}"
        
        # First try exact product name matches
        if any(word in name.lower() for word in ['rehau', 'acrylic', 'solid surface', 'laminate']):
            category = Category.objects.get(name='Laminates')
            product.category = category
            product.save()
            updates_count += 1
            print(f"Updated {product.name} to category: Laminates")
            continue
            
        if 'lock' in name.lower() and ('smart' in name.lower() or 'qubo' in name.lower()):
            category = Category.objects.get(name='Digital Locks')
            product.category = category
            product.save()
            updates_count += 1
            print(f"Updated {product.name} to category: Digital Locks")
            continue
            
        # Then try keyword matching for remaining products
        for category_name, keywords in category_rules.items():
            if any(keyword in product_text for keyword in keywords):
                category = Category.objects.get(name=category_name)
                if product.category != category:
                    product.category = category
                    product.save()
                    updates_count += 1
                    print(f"Updated {product.name} to category: {category_name}")
                matched = True
                break
        
        if not matched:
            unmatched_products.append(product.name)
    
    print(f"\nTotal products updated: {updates_count}")
    
    if unmatched_products:
        print("\nProducts without category matches:")
        for product_name in unmatched_products:
            print(f"- {product_name}")

if __name__ == "__main__":
    print("Creating main categories...")
    create_categories()
    
    print("\nUpdating product categories...")
    update_product_categories()