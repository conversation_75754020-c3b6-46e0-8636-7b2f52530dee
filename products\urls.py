# from django.urls import path, include
# from .views import CategoryViewSet,ProductViewSet,ProductVariantViewSet,ProductImageViewSet,ReviewViewSet
# from rest_framework.routers import DefaultRouter

# router = DefaultRouter()
# router.register(r'categories', CategoryViewSet, basename='category')
# router.register(r'products', ProductViewSet, basename='product')
# router.register(r'product-variants', ProductVariantViewSet, basename='product-variant')
# router.register(r'product-images', ProductImageViewSet, basename='product-image')

# urlpatterns = [
#     path('', include(router.urls)),
# ]

from django.urls import path
from .views import (
    CategoryListView,
    CategoryDetailView,
    root_categories,
    ProductListView,
    ProductDetailView,
    add_product_variant,
    add_product_image,
    add_review,
    featured_products,
    ProductVariantListView,
    ProductImageListView,
    ReviewListView,
    ReviewDetailView,
    BrandListView,
    AdminCategoryListView,
    CategoryCreateView,
    CategoryUpdateView,
    CategoryProductsAPIView)

urlpatterns = [
    # Category URLs
    path("categories/", CategoryListView.as_view(), name="category-list"),
    path("brands/", BrandListView.as_view(), name="brands-list"),
    path(
        "categories/<slug:slug>/", CategoryDetailView.as_view(), name="category-detail"
    ),
    path("categories/root/", root_categories, name="category-root"),

    path('categories/<slug:slug>/products/', CategoryProductsAPIView.as_view(), name='category-products'),
  
    # Product URLs
    path("", ProductListView.as_view(), name="product-list"),
    path("<slug:slug>/", ProductDetailView.as_view(), name="product-detail"),
    path("<slug:slug>/add-variant/", add_product_variant, name="product-add-variant"),
    path("<slug:slug>/add-image/", add_product_image, name="product-add-image"),
    path("<slug:slug>/add-review/", add_review, name="product-add-review"),
    path("feature/products/", featured_products, name="product-featured"),
    # Product Variant URLs
    path(
        "<slug:product_slug>/variants/",
        ProductVariantListView.as_view(),
        name="product-variant-list",
    ),
    # Product Image URLs
    path(
        "<slug:product_slug>/images/",
        ProductImageListView.as_view(),
        name="product-image-list",
    ),
    # Review URLs
    path("<slug:product_slug>/reviews/", ReviewListView.as_view(), name="review-list"),
    path(
        "<slug:product_slug>/reviews/<int:pk>/",
        ReviewDetailView.as_view(),
        name="review-detail",
    ),
    path("admin/categories/", AdminCategoryListView.as_view(), name="category-list"),
    path(
        "api/categories/create/", CategoryCreateView.as_view(), name="category-create"
    ),
    path(
        "api/categories/<int:pk>/update/",
        CategoryUpdateView.as_view(),
        name="category-update",
    ),
]
