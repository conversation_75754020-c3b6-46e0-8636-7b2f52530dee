from rest_framework import serializers


class ProductPerformanceSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    sales = serializers.IntegerField()
    revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    growth = serializers.CharField()

class SalesChartSerializer(serializers.Serializer):
    name = serializers.CharField()
    sales = serializers.IntegerField()

class CustomerDemographicSerializer(serializers.Serializer):
    name = serializers.CharField()
    value = serializers.IntegerField()
