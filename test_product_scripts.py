#!/usr/bin/env python3
"""
Test Script for Product Description Scripts

This script tests the database connection and validates that the
product description generation and update scripts will work properly.

Features:
- Tests database connection
- Validates product data structure
- Checks required dependencies
- Provides sample data for testing

Author: Triumph Enterprise
Created: 2025
"""

import os
import sys
import django
import json
import logging
from datetime import datetime

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from django.db import connection
from products.models import Product, Category, Brand, ProductImage, SubCategorie, GST

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test database connection"""
    try:
        logger.info("Testing database connection...")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT version()")
            db_version = cursor.fetchone()[0]
            logger.info(f"Database connection successful! PostgreSQL version: {db_version}")
            
        return True
        
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False

def test_product_models():
    """Test product models and relationships"""
    try:
        logger.info("Testing product models...")
        
        # Test basic counts
        total_products = Product.objects.count()
        active_products = Product.objects.filter(is_active=True).count()
        total_categories = Category.objects.count()
        total_brands = Brand.objects.count()
        
        logger.info(f"Total products: {total_products}")
        logger.info(f"Active products: {active_products}")
        logger.info(f"Total categories: {total_categories}")
        logger.info(f"Total brands: {total_brands}")
        
        if total_products == 0:
            logger.warning("No products found in database!")
            return False
        
        # Test a sample product
        sample_product = Product.objects.select_related(
            'category', 'brand', 'gst'
        ).prefetch_related('images').first()
        
        if sample_product:
            logger.info(f"Sample product: {sample_product.name}")
            logger.info(f"  Category: {sample_product.category.name if sample_product.category else 'None'}")
            logger.info(f"  Brand: {sample_product.brand.name if sample_product.brand else 'None'}")
            logger.info(f"  Price: {sample_product.price}")
            logger.info(f"  GST Rate: {sample_product.get_gst_rate().rate}%")
            logger.info(f"  Images: {sample_product.images.count()}")
            logger.info(f"  Description length: {len(sample_product.description or '')}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing product models: {str(e)}")
        return False

def test_required_dependencies():
    """Test required Python dependencies"""
    try:
        logger.info("Testing required dependencies...")
        
        required_modules = [
            'requests',
            'django',
            'json',
            'logging',
            'datetime',
            'urllib.parse',
            're',
            'dataclasses',
            'pathlib'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                logger.debug(f"✓ {module}")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"✗ {module}")
        
        if missing_modules:
            logger.error(f"Missing required modules: {missing_modules}")
            return False
        
        logger.info("All required dependencies are available")
        return True
        
    except Exception as e:
        logger.error(f"Error testing dependencies: {str(e)}")
        return False

def test_output_directory():
    """Test output directory creation and permissions"""
    try:
        logger.info("Testing output directory...")
        
        output_dir = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Test write permissions
        test_file = os.path.join(output_dir, "test_write_permissions.txt")
        with open(test_file, 'w') as f:
            f.write("Test file for write permissions")
        
        # Clean up test file
        os.remove(test_file)
        
        logger.info(f"Output directory is accessible: {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Error testing output directory: {str(e)}")
        return False

def create_sample_json():
    """Create a sample JSON file for testing the update script"""
    try:
        logger.info("Creating sample JSON file for testing...")
        
        # Get a few sample products
        sample_products = Product.objects.select_related(
            'category', 'brand', 'gst'
        ).prefetch_related('images')[:3]
        
        if not sample_products:
            logger.warning("No products available to create sample JSON")
            return None
        
        sample_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "total_products": len(sample_products),
                "script_version": "1.0-test",
                "description_source": "Test data"
            },
            "products": []
        }
        
        for product in sample_products:
            # Get product images
            images = []
            for img in product.images.all():
                if img.image:
                    images.append(img.image.name)
            
            product_data = {
                "id": product.id,
                "name": product.name,
                "slug": product.slug,
                "description": product.description or "",
                "enhanced_description": f"ENHANCED: {product.name} - This is a high-quality product with excellent features and performance. Designed for optimal user experience and reliability. Perfect for both professional and personal use.",
                "category": product.category.name if product.category else None,
                "subcategory": product.subcategory.name if product.subcategory else None,
                "brand": product.brand.name if product.brand else None,
                "price": float(product.price),
                "mrp": float(product.mrp),
                "base_price": float(product.base_price),
                "gst_rate": float(product.get_gst_rate().rate),
                "gst_amount": float(product.calculate_gst_from_mrp()),
                "stock": product.stock,
                "is_active": product.is_active,
                "created_at": product.created_at.isoformat(),
                "updated_at": product.updated_at.isoformat(),
                "images": images
            }
            
            sample_data["products"].append(product_data)
        
        # Save sample JSON
        output_dir = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        os.makedirs(output_dir, exist_ok=True)
        
        sample_file = os.path.join(output_dir, "sample_enhanced_products_test.json")
        
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"Sample JSON file created: {sample_file}")
        return sample_file
        
    except Exception as e:
        logger.error(f"Error creating sample JSON: {str(e)}")
        return None

def run_all_tests():
    """Run all tests and provide summary"""
    try:
        logger.info("Starting comprehensive test suite...")
        
        tests = [
            ("Database Connection", test_database_connection),
            ("Product Models", test_product_models),
            ("Required Dependencies", test_required_dependencies),
            ("Output Directory", test_output_directory),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result:
                    logger.info(f"✓ {test_name} PASSED")
                else:
                    logger.error(f"✗ {test_name} FAILED")
                    
            except Exception as e:
                logger.error(f"✗ {test_name} ERROR: {str(e)}")
                results[test_name] = False
        
        # Create sample JSON if all tests pass
        if all(results.values()):
            logger.info(f"\n{'='*50}")
            logger.info("Creating sample test data...")
            logger.info(f"{'='*50}")
            sample_file = create_sample_json()
            if sample_file:
                results["Sample JSON Creation"] = True
            else:
                results["Sample JSON Creation"] = False
        
        # Print summary
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        passed = 0
        failed = 0
        
        for test_name, result in results.items():
            status = "PASS" if result else "FAIL"
            print(f"{test_name:<30} {status}")
            if result:
                passed += 1
            else:
                failed += 1
        
        print("-" * 60)
        print(f"Total tests: {len(results)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print("="*60)
        
        if failed == 0:
            print("🎉 All tests passed! The scripts should work properly.")
            print("\nNext steps:")
            print("1. Run: python generate_product_descriptions.py")
            print("2. Run: python update_product_descriptions.py")
        else:
            print("❌ Some tests failed. Please fix the issues before running the scripts.")
        
        return failed == 0
        
    except Exception as e:
        logger.error(f"Error running test suite: {str(e)}")
        return False

def main():
    """Main function"""
    try:
        print("Product Description Scripts Test Suite")
        print("="*60)
        
        success = run_all_tests()
        
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test suite execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
