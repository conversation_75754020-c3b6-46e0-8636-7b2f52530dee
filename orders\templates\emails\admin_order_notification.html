<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Notification - Triumph Enterprises</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
        h1 {
            color: #4a5568;
        }
        .order-details {
            background-color: #edf2f7;
            padding: 15px;
            border-left: 4px solid #4a5568;
            margin: 15px 0;
        }
        .payment-status {
            padding: 12px 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .payment-status p {
            margin: 5px 0;
        }
        .payment-status.success {
            background-color: #f0fff4;
            border: 2px solid #48bb78;
            color: #2f855a;
        }
        .payment-status.failure {
            background-color: #fff5f5;
            border: 2px solid #e53e3e;
            color: #c53030;
        }
        .payment-status.pending {
            background-color: #fffaf0;
            border: 2px solid #ed8936;
            color: #c05621;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .order-items th, .order-items td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .order-items th {
            background-color: #f2f2f2;
        }
        .total-row {
            font-weight: bold;
        }
        .customer-info {
            background-color: #e6f7ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .admin-button {
            display: inline-block;
            background-color: #4a5568;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Order Received</h1>
    </div>

    <div class="content">
        <p>A new order has been placed on your website.</p>

        <div class="order-details">
            <p><strong>Order Number:</strong> {{ order.id }}</p>
            <p><strong>Order Date:</strong> {{ order.created_at|date:"F d, Y, g:i a" }}</p>
            <p><strong>Payment Method:</strong> {{ payment_method }}</p>
            <p><strong>Order Status:</strong> {{ order.status }}</p>
        </div>

        {% if payment_status %}
        <div class="payment-status {% if payment_status == 'COMPLETED' %}success{% elif payment_status == 'FAILED' %}failure{% else %}pending{% endif %}">
            <p>Payment Status: {{ payment_status|title }}</p>
            {% if payment_status == 'COMPLETED' and order.status == 'PAID' %}
            <p>Payment has been successfully processed.</p>
            {% elif payment_status == 'FAILED' or order.status == 'PAYMENT_FAILED' %}
            <p>Payment could not be processed. Customer may need assistance.</p>
            {% else %}
            <p>Payment is being processed.</p>
            {% endif %}
        </div>
        {% endif %}

        <div class="customer-info">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> {{ order.user.name }} </p>
            <p><strong>Email:</strong> {{ order.user.email }}</p>
            <p><strong>Phone:</strong> {{ order.user.phone_number|default:"Not provided" }}</p>
        </div>

        <h3>Order Summary</h3>
        <table class="order-items">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Quantity</th>
                    <th>Price</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.items.all %}
                <tr>
                    <td>{{ item.product_name }}{% if item.variant_name %} - {{ item.variant_name }}{% endif %}</td>
                    <td>{{ item.quantity }}</td>
                    <td>₹{{ item.total_price }}</td>
                </tr>
                {% endfor %}
                <tr>
                    <td colspan="2"><strong>Subtotal (before GST)</strong></td>
                    <td>₹{{ order.subtotal }}</td>
                </tr>
                {% if order.igst_amount and order.igst_amount > 0 %}
                <tr>
                    <td colspan="2">IGST (18%)</td>
                    <td>₹{{ order.igst_amount }}</td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="2">CGST (9%)</td>
                    <td>₹{% if order.cgst_amount %}{{ order.cgst_amount }}{% else %}{% widthratio order.subtotal 100 9 %}{% endif %}</td>
                </tr>
                <tr>
                    <td colspan="2">SGST (9%)</td>
                    <td>₹{% if order.sgst_amount %}{{ order.sgst_amount }}{% else %}{% widthratio order.subtotal 100 9 %}{% endif %}</td>
                </tr>
                {% endif %}
                <tr>
                    <td colspan="2"><strong>Shipping</strong></td>
                    <td>₹{{ order.shipping_cost }}</td>
                </tr>
                <tr class="total-row">
                    <td colspan="2"><strong>Total Amount</strong></td>
                    <td>₹{{ order.total }}</td>
                </tr>
            </tbody>
        </table>

        <div class="shipping-info">
            <h3>Shipping Information</h3>
            <p><strong>Shipping Method:</strong> {{ order.shipping_method.name }}</p>
            <p><strong>Estimated Delivery:</strong> {{ order.estimated_delivery_date|date:"F d, Y" }}</p>
            <p><strong>Shipping Address:</strong><br>
                {{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}<br>
                {{ order.shipping_address.street_address }}<br>
                {% if order.shipping_address.apartment %}{{ order.shipping_address.apartment }}<br>{% endif %}
                {{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postal_code }}<br>
                {{ order.shipping_address.country }}
            </p>
        </div>

        <p>Please process this order at your earliest convenience.</p>
    </div>

    <div class="footer">
        <p>This is an automated notification from your website.</p>
        <p>&copy; 2024 Triumph Enterprises. All rights reserved.</p>
    </div>
</body>
</html>
