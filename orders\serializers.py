from rest_framework import serializers
from .models import Cart, CartItem, Order, OrderItem, ShippingMethod, Payment,Product, ProductVariant
from users.models import Address
from products.serializers import ProductSerializer, ProductVariantSerializer
from users.serializers import AddressSerializer, UserSerializer
from products.models import ProductImage

class CartItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        source='product',
        queryset=Product.objects.all()
    )
    variant = ProductVariantSerializer(read_only=True)
    variant_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        source='variant',
        queryset=ProductVariant.objects.all(),
        required=False,
        allow_null=True
    )
    line_total = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True
    )
    # Add GST information for each cart item
    gst_info = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_id', 'variant', 'variant_id',
                 'quantity', 'line_total', 'gst_info', 'created_at']
        read_only_fields = ['cart']

    def get_gst_info(self, obj):
        """Get GST information for this cart item"""
        from .gst_service import gst_service
        if obj.product:
            gst_calculation = gst_service.calculate_product_gst_from_mrp(obj.product, obj.quantity)
            return {
                'gst_rate': float(gst_calculation['gst_rate']),
                'base_amount': float(gst_calculation['base_amount']),
                'gst_amount': float(gst_calculation['gst_amount']),
                'cgst_amount': float(gst_calculation['cgst_amount']),
                'sgst_amount': float(gst_calculation['sgst_amount']),
                'igst_amount': float(gst_calculation['igst_amount']),
                'hsn_code': gst_calculation['hsn_code'],
                'unit_base_price': float(gst_calculation['unit_base_price']),
                'unit_mrp': float(gst_calculation['unit_mrp'])
            }
        return None

    def validate(self, data):
        product = data.get('product')
        variant = data.get('variant')
        quantity = data.get('quantity', 1)

        if variant and variant.product != product:
            raise serializers.ValidationError(
                {"variant": "This variant does not belong to the selected product."}
            )

        if product.stock < quantity:
            raise serializers.ValidationError(
                {"quantity": "Not enough stock available."}
            )

        return data

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_items = serializers.IntegerField(read_only=True)
    subtotal = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True
    )
    # Add GST breakdown fields
    gst_breakdown = serializers.SerializerMethodField()
    total_gst_amount = serializers.SerializerMethodField()
    total_cgst_amount = serializers.SerializerMethodField()
    total_sgst_amount = serializers.SerializerMethodField()
    total_igst_amount = serializers.SerializerMethodField()
    total_with_gst = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = [
            'id', 'items', 'total_items', 'subtotal', 'created_at', 'updated_at',
            'gst_breakdown', 'total_gst_amount', 'total_cgst_amount',
            'total_sgst_amount', 'total_igst_amount', 'total_with_gst'
        ]
        read_only_fields = ['user']

    def get_gst_breakdown(self, obj):
        """Get detailed GST breakdown for all cart items"""
        from .gst_service import gst_service
        if not obj.items.exists():
            return {
                'subtotal': 0,
                'total_gst_amount': 0,
                'total_cgst_amount': 0,
                'total_sgst_amount': 0,
                'total_igst_amount': 0,
                'total_with_gst': 0,
                'item_details': []
            }
        return gst_service.calculate_cart_gst_from_mrp(obj.items.all())

    def get_total_gst_amount(self, obj):
        """Get total GST amount for the cart"""
        breakdown = self.get_gst_breakdown(obj)
        return float(breakdown.get('total_gst_amount', 0))

    def get_total_cgst_amount(self, obj):
        """Get total CGST amount for the cart"""
        breakdown = self.get_gst_breakdown(obj)
        return float(breakdown.get('total_cgst_amount', 0))

    def get_total_sgst_amount(self, obj):
        """Get total SGST amount for the cart"""
        breakdown = self.get_gst_breakdown(obj)
        return float(breakdown.get('total_sgst_amount', 0))

    def get_total_igst_amount(self, obj):
        """Get total IGST amount for the cart"""
        breakdown = self.get_gst_breakdown(obj)
        return float(breakdown.get('total_igst_amount', 0))

    def get_total_with_gst(self, obj):
        """Get total amount including GST"""
        breakdown = self.get_gst_breakdown(obj)
        return float(breakdown.get('total_with_gst', 0))

class ShippingMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShippingMethod
        fields = '__all__'

class OrderItemSerializer(serializers.ModelSerializer):
    product_image = serializers.SerializerMethodField()
    class Meta:
        model = OrderItem
        fields = ['id', 'product_name', 'variant_name', 'quantity',
                 'unit_price', 'total_price', 'product_image']
        read_only_fields = ['product_name', 'variant_name', 'unit_price', 'total_price']

    def get_product_image(self, obj):
        image = ProductImage.objects.filter(product=obj.product).first()
        if image:
            return image.image.url

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    shipping_address = AddressSerializer()
    billing_address = AddressSerializer()
    shipping_method = ShippingMethodSerializer(read_only=True)
    shipping_method_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        source='shipping_method',
        queryset=ShippingMethod.objects.filter(is_active=True)
    )
    class Meta:
        model = Order
        fields = [
            'id', 'status', 'items', 'shipping_address', 'billing_address',
            'shipping_method', 'shipping_method_id', 'subtotal', 'gst_amount',
            'cgst_amount', 'sgst_amount', 'igst_amount', 'shipping_cost',
            'total', 'tracking_number', 'estimated_delivery_date', 'notes',
            'created_at', 'updated_at', 'user'
        ]
        read_only_fields = [
            'status', 'subtotal', 'gst_amount', 'cgst_amount', 'sgst_amount',
            'igst_amount', 'shipping_cost', 'total', 'tracking_number',
            'estimated_delivery_date', 'user'
        ]



class OrderCreateSerializer(serializers.ModelSerializer):
    shipping_address_id = serializers.PrimaryKeyRelatedField(
        queryset=Address.objects.all(),
        source='shipping_address'
    )
    billing_address_id = serializers.PrimaryKeyRelatedField(
        queryset=Address.objects.all(),
        source='billing_address'
    )
    shipping_method_id = serializers.PrimaryKeyRelatedField(
        queryset=ShippingMethod.objects.filter(is_active=True),
        source='shipping_method'
    )

    class Meta:
        model = Order
        fields = ['shipping_address_id', 'billing_address_id', 'shipping_method_id', 'notes']

    def validate(self, data):
        user = self.context['request'].user
        shipping_address = data.get('shipping_address')
        billing_address = data.get('billing_address')

        if shipping_address.user != user or billing_address.user != user:
            raise serializers.ValidationError("Invalid address selected.")

        # Get the cart to check minimum order value
        from .models import Cart
        cart, _ = Cart.objects.get_or_create(user=user)

        # Check if cart subtotal meets minimum order value
        if cart.subtotal < 150:
            raise serializers.ValidationError(
                {"detail": "Minimum order value should be ₹150 to place an order."}
            )

        return data

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ['id', 'amount', 'status', 'payment_method',
                 'transaction_id', 'created_at']
        read_only_fields = ['status', 'transaction_id']

class OrderDetailSerializer(OrderSerializer):
    payments = PaymentSerializer(many=True, read_only=True)

    class Meta(OrderSerializer.Meta):
        fields = OrderSerializer.Meta.fields + ['payments']


class UserDetailSerilizer(serializers.ModelSerializer):
    # orders = OrderSerializer(many=True, read_only=True)
    # user = UserSerializer(read_only=True)
    # shipping_address=AddressSerializer(read_only=True)
    # billing_address=AddressSerializer(read_only=True)
    # shipping_method = ShippingMethodSerializer(read_only=True)

    class Meta:
        model = Order
        fields = ["id", 'user', "status", "shipping_address", "billing_address",
                  "shipping_method", "subtotal", "gst_amount", "cgst_amount", "sgst_amount",
                  "igst_amount", "shipping_cost", "total","stripe_payment_intent_id",
                  "stripe_client_secret","tracking_number","estimated_delivery_date","notes","created_at","updated_at"]